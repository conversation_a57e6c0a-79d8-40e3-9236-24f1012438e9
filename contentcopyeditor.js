(function() {
  const fieldSelectors = [
      '#designCreator-productEditor-title',
      '#designCreator-productEditor-brandName',
      '#designCreator-productEditor-featureBullet1',
      '#designCreator-productEditor-featureBullet2',
      '#designCreator-productEditor-description'
  ];

  const characterLimits = {
      '#designCreator-productEditor-featureBullet1': 256,
      '#designCreator-productEditor-featureBullet2': 256,
      '#designCreator-productEditor-description': 2000
  };

  let skipConversion = false; 
  let productorCheckComplete = false; 

  function waitForElement(selector, callback) {
      const interval = setInterval(() => {
          const element = document.querySelector(selector);
          if (element) {
              clearInterval(interval);
              callback(element);
          }
      }, 100);
  }

  function dispatchInputEvent(element) {
      const event = new Event('input', { bubbles: true });
      element.dispatchEvent(event);
  }

  function adjustHeight(textarea) {
      if (!textarea) return;
      textarea.style.height = 'auto';
      const newHeight = `${textarea.scrollHeight}px`;
      textarea.style.height = newHeight;
  }

  function enableAutoResize(textarea) {
      if (!textarea) return;
      adjustHeight(textarea);
      textarea.addEventListener('input', function() {
          adjustHeight(textarea);
      });
  }

  function copyBulletsToDescription(descriptionTextarea) {
      const container = descriptionTextarea.closest('.col-6.pr-4.pb-3.pl-2');
      if (!container) {
          return;
      }
      const bullet1 = container.querySelector('[id$="featureBullet1"]');
      const bullet2 = container.querySelector('[id$="featureBullet2"]');
      const bullet1Textarea = bullet1 ? bullet1.nextElementSibling : null;
      const bullet2Textarea = bullet2 ? bullet2.nextElementSibling : null;
      let text1 = '';
      let text2 = '';

      if (bullet1) {
          if (bullet1Textarea && bullet1Textarea.tagName.toLowerCase() === 'textarea') {
              text1 = bullet1Textarea.value.trim();
          } else {
              text1 = bullet1.value.trim();
          }
      }
      if (bullet2) {
          if (bullet2Textarea && bullet2Textarea.tagName.toLowerCase() === 'textarea') {
              text2 = bullet2Textarea.value.trim();
          } else {
              text2 = bullet2.value.trim();
          }
      }
      if (text1 && !/[.!?]$/.test(text1)) {
          text1 += '.';
      }
      if (text2 && !/[.!?]$/.test(text2)) {
          text2 += '.';
      }
      const combinedText = text2 ? `${text1} ${text2}` : text1;
      descriptionTextarea.value = combinedText;
      dispatchInputEvent(descriptionTextarea);
      adjustHeight(descriptionTextarea);
  }

  function getLanguageContainerFromButton(button) {
      let node = button;
      while (node && node.getRootNode && node.getRootNode().host) {
          node = node.getRootNode().host;
      }
      const languageContainer = node.closest('[id]');
      if (languageContainer) {
          return languageContainer;
      }
      return null;
  }

  function copyLangFields(languageContainer) {
      if (!languageContainer) {
          return;
      }
      const langCode = languageContainer.id;
      if (!langCode) {
          return;
      }
      const fields = languageContainer.querySelectorAll(fieldSelectors.join(', '));
      const dataToCopy = {};
      fields.forEach(field => {
          const fieldId = field.getAttribute('id');
          dataToCopy[fieldId] = field.value;
      });
      const jsonData = JSON.stringify(dataToCopy);
      localStorage.setItem('sharedClipboard', jsonData);
  }

  function pasteLangFields(languageContainer) {
      if (!languageContainer) {
          return;
      }
      const langCode = languageContainer.id;
      if (!langCode) {
          return;
      }
      const jsonData = localStorage.getItem('sharedClipboard');
      if (!jsonData) {
          return;
      }
      try {
          const data = JSON.parse(jsonData);
          const fields = languageContainer.querySelectorAll(fieldSelectors.join(', '));
          fields.forEach(field => {
              const fieldId = field.getAttribute('id');
              if (data[fieldId] !== undefined) {
                  field.value = data[fieldId];
                  dispatchInputEvent(field);
                  if (field.tagName.toLowerCase() === 'textarea') {
                      adjustHeight(field);
                  }
              }
          });
          processAllRows();
      } catch (err) {}
  }

  function expandAllSections(callback) {
      const possibleLangs = ['en', 'de', 'fr', 'it', 'es', 'ja'];
      let expandedCount = 0;
      possibleLangs.forEach(lang => {
          const btn = document.querySelector(`button.btn.btn-link[aria-controls="${lang}"]`);
          if (btn && btn.getAttribute('aria-expanded') === 'false') {
              btn.click();
              expandedCount++;
          }
      });
      if (callback && expandedCount > 0) {
          setTimeout(callback, 500);
      } else if (callback) {
          callback();
      }
  }

  function pasteAllFieldsToAll() {
      const radio = document.querySelector('#translation-request-no');
      if (radio && !radio.checked) {
          radio.checked = true;
          radio.dispatchEvent(new Event('change', { bubbles: true }));
      }

      expandAllSections(() => {
          const jsonData = localStorage.getItem('sharedClipboard');
          if (!jsonData) {
              return;
          }
          try {
              const data = JSON.parse(jsonData);
              const possibleLangs = ['en', 'de', 'fr', 'it', 'es', 'ja'];
              for (const langCode of possibleLangs) {
                  const container = document.getElementById(langCode);
                  if (!container) {
                      continue;
                  }
                  const fields = container.querySelectorAll(fieldSelectors.join(', '));
                  fields.forEach(field => {
                      const fieldId = field.getAttribute('id');
                      if (data[fieldId] !== undefined) {
                          field.value = data[fieldId];
                          dispatchInputEvent(field);
                          if (field.tagName.toLowerCase() === 'textarea') {
                              adjustHeight(field);
                          }
                      }
                  });
              }
              processAllRows();
          } catch (err) {}
      });
  }

  function performReplacements(replacements, languageContainer) {
      if(!languageContainer) {
          return;
      }
      fieldSelectors.forEach(selector => {
          const field = languageContainer.querySelector(selector);
          if(field){
              replacements.forEach(pair => {
                  const findText = pair.find;
                  const replaceText = pair.replace;
                  if(findText !== ""){
                      const escaped = findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                      const regex = new RegExp(`\\b${escaped}\\b`, 'gi');
                      let safeReplaceText = replaceText.replace(/\$/g, '$$$$');
                      field.value = field.value.replace(regex, safeReplaceText);
                      dispatchInputEvent(field);
                      if(field.tagName.toLowerCase() === 'textarea'){
                          adjustHeight(field);
                      }
                  }
              });
          }
      });
  }

  function attachCharLimit(field, limit) {
      if (!field) return;
      if (field._charLimitAttached) return;
      field.addEventListener('input', function() {
          if (this.value.length > limit) {
              this.value = this.value.substring(0, limit);
              dispatchInputEvent(this);
          }
          let smallElem = this.parentNode.querySelector('small');
          if (smallElem) {
              const remaining = limit - this.value.length;
              smallElem.textContent = `${remaining} characters remaining`;
              smallElem.style.color = remaining === 0 ? '#cd103d' : '';
          }
      });
      field._charLimitAttached = true;
  }

  function applyCharacterLimitsToContainer(container) {
      if (!container) return;
      Object.entries(characterLimits).forEach(([selector, limit]) => {
          let field = container.querySelector(selector);
          if(field){
              if(productorCheckComplete && !skipConversion && 
                 (selector === '#designCreator-productEditor-featureBullet1' || selector === '#designCreator-productEditor-featureBullet2') 
                 && field.tagName.toLowerCase() !== 'textarea') {
                  const originalInput = field;
                  const newTextarea = document.createElement('textarea');
                  newTextarea.id = originalInput.id;
                  newTextarea.className = originalInput.className;
                  newTextarea.value = originalInput.value;
                  newTextarea.placeholder = originalInput.placeholder || '';
                  newTextarea.rows = 4;
                  originalInput.removeAttribute('id');
                  originalInput.style.display = 'none';
                  originalInput.parentNode.insertBefore(newTextarea, originalInput.nextSibling);
                  
                  enableAutoResize(newTextarea);
                  newTextarea.addEventListener('input', function() {
                      originalInput.value = newTextarea.value;
                      dispatchInputEvent(originalInput);
                  });
                  field = newTextarea;
              }
              attachCharLimit(field, limit);
          }
      });
  }

  function revertUnexpectedTextareas() {
      const mappings = [
           { prefix: 'prdm-copy-find-', newPrefix: 'find-', placeholder: 'e.g. T-Shirt' },
           { prefix: 'prdm-copy-replace-', newPrefix: 'replace-', placeholder: 'e.g. Design or leave empty' }
      ];
      
      mappings.forEach(mapping => {
          document.querySelectorAll(`textarea[id^="${mapping.prefix}"]`).forEach(textarea => {
              const langSuffix = textarea.id.substring(mapping.prefix.length);
              const input = document.createElement('input');
              input.type = 'text';
              input.className = textarea.className;
              input.id = mapping.newPrefix + langSuffix;
              input.placeholder = mapping.placeholder;
              input.value = textarea.value;
              input.style.borderRadius = '0.25rem';
              input.style.border = '0px';
              
              if (textarea.hasAttribute('maxlength')) {
                  input.maxLength = textarea.getAttribute('maxlength');
              }
              
              const storageKey = input.id + '_value';
              const saved = localStorage.getItem(storageKey);
              if(saved !== null) {
                  input.value = saved;
              }
              
              input.addEventListener('input', () => {
                  localStorage.setItem(storageKey, input.value);
              });
              
              textarea.parentNode.replaceChild(input, textarea);
          });
      });
  }

  function injectNewUI(containerElement) {
      if (!containerElement) {
          return;
      }
      const shadowRoot = containerElement.attachShadow({ mode: 'open' });

      const langContainer = containerElement.closest('[id]');
      const langCode = langContainer ? langContainer.id : 'default';

      const styles = `
          * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
              font-family: "Amazon Ember", sans-serif;
          }
          .copy-find-replace {
              width: 100%;
              margin: 0;
              padding: 12px;
              display: flex;
              flex-direction: column;
              gap: 10px;
              min-width: 0px;
              padding-left: 20px;
              padding-right: 20px;
              padding-top: 12.5px;
              box-sizing: border-box;
              justify-content: space-between;
              align-items: flex-start;
          }
          .toolbar {
              display: flex;
              align-items: center;
              width: 100%;
              margin-bottom: 10px;
          }
          .button-group button {
              padding: 12px 48px;
              border: none;
              border-radius: 4px;
              color: white;
              cursor: pointer;
              font-size: 14px;
              font-weight: 500;
              background-color: #470CED;
              margin-right: 4px;
          }
          .button-group button:hover {
              background-color: #2A00A0;
          }
          .toggle-container {
              margin-left: auto;
              display: flex;
              align-items: center;
              gap: 8px;
          }
          .toggle {
              width: 36px;
              height: 18px;
              border-radius: 18px;
              background-color: rgb(207, 212, 212);
              position: relative;
              cursor: pointer;
              transition: background-color 0.3s;
          }
          .toggle.active {
              background-color: #470CED;
          }
          .toggle::after {
              content: '';
              position: absolute;
              width: 12px;
              height: 12px;
              border-radius: 50%;
              background-color: white;
              top: 3px;
              left: 3px;
              transition: transform 0.3s;
          }
          .toggle.active::after {
              transform: translateX(18px);
          }
          .header {
              margin-bottom: 10px;
          }
          .header h1 {
              font-size: 14px;
              color: #111;
              margin-bottom: 0px;
          }
          .header p {
              color: #667575;
              font-size: 12px;
          }
          .find-replace-section {
              width: 100%;
              transition: opacity 0.3s, max-height 0.3s;
              max-height: 1000px;
              opacity: 1;
              overflow: hidden;
          }
          .find-replace-section.hidden {
              opacity: 0;
              max-height: 0;
              display: none;
          }
          .find-replace-container {
              display: flex;
              flex-direction: column;
              gap: 24px;
              margin-bottom: 24px;
          }
          .find-replace-row {
              display: grid;
              grid-template-columns: 1fr 1fr auto;
              gap: 16px;
              align-items: flex-end;
          }
          .input-group {
              display: flex;
              flex-direction: column;
              gap: 8px;
          }
          .input-group label {
              font-size: 13px;
              font-weight: 500;
              color: #374151;
          }
          .input-group input[type="text"] {
              padding: 8px 12px;
              border: 1.5px solid #d1d5db;
              border-radius: 2px;
              font-size: 13px;
              transition: border-color 0.2s ease;
          }
          .input-group input[type="text"]:focus {
              outline: none;
              border-color: #470CED;
              border-width: 1.5px;
          }
          .input-group input[type="text"]::placeholder {
              color: #9ca3af;
          }
          .action-button {
              width: 36px;
              height: 36px;
              border: 1pt dashed #d1d5db;
              border-radius: 4px;
              background: none;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #008296;
              font-size: 14px;
          }
          .action-button img {
              max-width: 16px;
          }
          .action-button.remove {
              color: #ef4444;
          }
          .action-button:disabled {
              opacity: 0.5;
              cursor: not-allowed;
          }
          .replace-button {
              width: 100%;
              padding: 12px;
              background-color: #470CED;
              color: white;
              border: none;
              border-radius: 4px;
              font-size: 16px;
              font-weight: 500;
              cursor: pointer;
          }
          .replace-button:hover {
              background-color: #2A00A0;
          }
          @media (max-width: 768px) {
              .find-replace-row {
                  grid-template-columns: 1fr;
              }
              .action-button {
                  margin-top: 8px;
              }
          }
      `;
      const styleElement = document.createElement('style');
      styleElement.textContent = styles;
      shadowRoot.appendChild(styleElement);

      const mainContainer = document.createElement('div');
      mainContainer.className = 'copy-find-replace';
      mainContainer.innerHTML = `
          <div class="toolbar">
              <div class="button-group">
                  <button class="button primary" id="copyBtn">Copy</button>
                  <button class="button primary" id="pasteBtn">Paste</button>
                  <button class="button primary" id="pasteAllBtn">Paste to All</button>
                  <button class="button primary" id="copyBulletsBtn">Copy Bullets to Description</button>
              </div>
              <div class="toggle-container">
                  <span>Find and replace</span>
                  <div class="toggle" id="toggle"></div>
              </div>
          </div>
          <div class="find-replace-section hidden" id="findReplaceSection">
              <div class="header">
                  <h1>Find and replace</h1>
                  <p>Find and replace keywords easily. Add up to 4 find-and-replace fields.</p>
              </div>
              <div class="find-replace-container" id="findReplaceContainer"></div>
              <button class="replace-button">Replace</button>
          </div>
      `;
      shadowRoot.appendChild(mainContainer);

      const copyBtn = shadowRoot.querySelector('#copyBtn');
      const pasteBtn = shadowRoot.querySelector('#pasteBtn');
      const pasteAllBtn = shadowRoot.querySelector('#pasteAllBtn');
      const copyBulletsBtn = shadowRoot.querySelector('#copyBulletsBtn');
      const toggle = shadowRoot.querySelector('#toggle');
      const findReplaceSection = shadowRoot.querySelector('#findReplaceSection');
      const findReplaceContainer = shadowRoot.querySelector('#findReplaceContainer');
      const replaceButton = shadowRoot.querySelector('.replace-button');

      if (copyBtn) {
          copyBtn.addEventListener('click', function() {
              const languageContainer = getLanguageContainerFromButton(this);
              if (languageContainer) {
                  copyLangFields(languageContainer);
              }
          });
      }

      if (pasteBtn) {
          pasteBtn.addEventListener('click', function() {
              const languageContainer = getLanguageContainerFromButton(this);
              if (languageContainer) {
                  pasteLangFields(languageContainer);
              }
          });
      }

      if (pasteAllBtn) {
          pasteAllBtn.addEventListener('click', pasteAllFieldsToAll);
      }

      if (copyBulletsBtn) {
          copyBulletsBtn.addEventListener('click', function() {
              const languageContainer = getLanguageContainerFromButton(this);
              if (languageContainer) {
                  const descriptionTextarea = languageContainer.querySelector('[id$="description"]');
                  if (descriptionTextarea) {
                      copyBulletsToDescription(descriptionTextarea);
                  }
              }
          });
      }

      const savedToggleState = localStorage.getItem(`toggleState_${langCode}`);
      if (savedToggleState === 'active') {
          if (toggle) {
              toggle.classList.add('active');
          }
          if (findReplaceSection) {
              findReplaceSection.classList.remove('hidden');
              findReplaceSection.style.display = '';
          }
      } else if (findReplaceSection) {
          findReplaceSection.classList.add('hidden');
      }

      if (toggle) {
          toggle.addEventListener('click', function() {
              const isActive = this.classList.toggle('active');
              if (findReplaceSection) {
                  if (isActive) {
                      findReplaceSection.classList.remove('hidden');
                      findReplaceSection.style.display = '';
                  } else {
                      findReplaceSection.classList.add('hidden');
                  }
              }
              localStorage.setItem(`toggleState_${langCode}`, isActive ? 'active' : 'inactive');
              processAllRows();
          });
      }

      function savePreferences() {
          if (!findReplaceContainer) return;
          const rows = findReplaceContainer.querySelectorAll('.find-replace-row');
          const preferences = [];
          rows.forEach(row => {
              const inputs = row.querySelectorAll('input[type="text"]');
              if(inputs.length >= 2) {
                  const findVal = inputs[0].value.trim();
                  const replaceVal = inputs[1].value.trim();
                  if(findVal !== "" || replaceVal !== "") {
                      preferences.push({
                          find: findVal,
                          replace: replaceVal
                      });
                  }
              }
          });
          localStorage.setItem(`findReplacePreferences_${langCode}`, JSON.stringify(preferences));
      }

      function createRow(isFirst = false) {
          const row = document.createElement('div');
          row.className = 'find-replace-row';

          const findGroup = document.createElement('div');
          findGroup.className = 'input-group';
          const findLabel = document.createElement('label');
          findLabel.textContent = 'Find';
          const findInput = document.createElement('input');
          findInput.type = 'text';
          findInput.placeholder = 'e.g., basketball';
          findGroup.appendChild(findLabel);
          findGroup.appendChild(findInput);

          const replaceGroup = document.createElement('div');
          replaceGroup.className = 'input-group';
          const replaceLabel = document.createElement('label');
          replaceLabel.textContent = 'Replace with';
          const replaceInput = document.createElement('input');
          replaceInput.type = 'text';
          replaceInput.placeholder = 'e.g., soccer';
          replaceGroup.appendChild(replaceLabel);
          replaceGroup.appendChild(replaceInput);

          const actionButton = document.createElement('button');
          actionButton.className = 'action-button';
          if (!isFirst) {
              actionButton.classList.add('remove');
          }

          const icon = document.createElement('img');
          if (isFirst) {
              icon.src = chrome.runtime.getURL('assets/add-row-ic.svg');
              icon.alt = 'Add row';
          } else {
              icon.src = chrome.runtime.getURL('assets/clear.svg');
              icon.alt = 'Remove row';
          }
          actionButton.appendChild(icon);

          if (!isFirst) {
              actionButton.addEventListener('click', function() {
                  row.remove();
                  updateAddButton();
                  savePreferences();
              });
          }

          [findInput, replaceInput].forEach(input => {
              input.addEventListener('input', savePreferences);
          });

          row.appendChild(findGroup);
          row.appendChild(replaceGroup);
          row.appendChild(actionButton);
          return row;
      }

      function updateAddButton() {
          const rowCount = findReplaceContainer.children.length;
          const addButton = findReplaceContainer.querySelector('.action-button');
          if (addButton) {
              addButton.disabled = rowCount >= 4;
          }
      }

      if (findReplaceContainer) {
          const savedPrefs = localStorage.getItem(`findReplacePreferences_${langCode}`);
          if (savedPrefs) {
              try {
                  const savedReplacements = JSON.parse(savedPrefs);
                  savedReplacements.forEach((pair, index) => {
                      if(index === 0) {
                          findReplaceContainer.appendChild(createRow(true));
                          const firstRow = findReplaceContainer.querySelector('.find-replace-row');
                          if (firstRow) {
                              const inputs = firstRow.querySelectorAll('input[type="text"]');
                              if(inputs.length >= 2) {
                                  inputs[0].value = pair.find;
                                  inputs[1].value = pair.replace;
                              }
                          }
                      } else {
                          const newRow = createRow();
                          const inputs = newRow.querySelectorAll('input[type="text"]');
                          if(inputs.length >= 2) {
                              inputs[0].value = pair.find;
                              inputs[1].value = pair.replace;
                          }
                          findReplaceContainer.appendChild(newRow);
                      }
                  });
                  updateAddButton();
              } catch(e) {}
          } else {
              findReplaceContainer.appendChild(createRow(true));
          }
          
          const addButton = findReplaceContainer.querySelector('.action-button');
          if (addButton) {
              addButton.addEventListener('click', function() {
                  if (findReplaceContainer) {
                      findReplaceContainer.appendChild(createRow());
                      updateAddButton();
                      savePreferences();
                  }
              });
          }
      }

      if (replaceButton) {
          replaceButton.addEventListener('click', function() {
              if (!findReplaceContainer) return;
              const rows = findReplaceContainer.querySelectorAll('.find-replace-row');
              const replacements = [];
              rows.forEach(row => {
                  const inputs = row.querySelectorAll('input[type="text"]');
                  if (inputs.length >= 2 && inputs[0].value && inputs[1].value) {
                      replacements.push({
                          find: inputs[0].value,
                          replace: inputs[1].value
                      });
                  }
              });
              if (replacements.length > 0) {
                  localStorage.setItem(`findReplacePreferences_${langCode}`, JSON.stringify(replacements));
                  const languageContainer = document.getElementById(langCode);
                  performReplacements(replacements, languageContainer);
              }
          });
      }
  }

  function injectContainerIntoRow(formRow) {
      let langContainer = formRow.closest('[id]');
      
      if (langContainer && langContainer.querySelector('.snap-copy-find-container')) {
          return;
      }

      const snapContainer = document.createElement('div');
      snapContainer.className = 'snap-copy-find-container';
      snapContainer.style.display = 'flex';
      snapContainer.style.width = '100%';
      formRow.insertBefore(snapContainer, formRow.firstChild);
      injectNewUI(snapContainer);

      const col1 = formRow.querySelector('.col-6.pr-2.pb-3.pl-4');
      const col2 = formRow.querySelector('.col-6.pr-4.pb-3.pl-2');
      if (col1 && col2) {
          const wrapper = document.createElement('div');
          wrapper.style.display = 'flex';
          wrapper.style.width = '100%';
          col1.style.width = '50%';
          col2.style.width = '50%';
          wrapper.appendChild(col1);
          wrapper.appendChild(col2);
          formRow.appendChild(wrapper);
      }
  }

  function processAllRows() {
      const accordion = document.querySelector('ngb-accordion');
      if (!accordion) return;
      const formRows = accordion.querySelectorAll('.form-row');
      
      formRows.forEach(formRow => {
          [...formRow.children].forEach(child => child.style.flexGrow = '1');
          injectContainerIntoRow(formRow);
      });

      if(!skipConversion){
          const languageContainers = document.querySelectorAll('[id]');
          languageContainers.forEach(container => {
              applyCharacterLimitsToContainer(container);
          });
      }

      revertUnexpectedTextareas();
  }

  function initialize() {
      processAllRows();
      
      setTimeout(() => {
          if(document.querySelector('.productor-style-container') || document.querySelector('.productor-manage-menu')){
              skipConversion = true;
          }
          productorCheckComplete = true;
          processAllRows();
      }, 4000);
  }

  function setupObserver() {
      const target = document.body;
      if (!target) return;
      const observer = new MutationObserver(() => {
          observer.disconnect();
          processAllRows();
          observer.observe(target, { childList: true, subtree: true });
      });
      observer.observe(target, { childList: true, subtree: true });
  }

  if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
          initialize();
          setupObserver();
      });
  } else {
      initialize();
      setupObserver();
  }
})();
  