const styleSheet = document.createElement("style");
styleSheet.textContent = `
    .apply-btn-container {
        padding: 0 !important;
        margin: 10px 0 0 0 !important;
    }

    .resize-btn {
        all: unset !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 8px !important;
        padding: 0 16px !important;
        height: 44px !important;
        width: 100% !important;
        border-radius: 4px !important;
        cursor: pointer !important;
        background-color: #470CED !important;
        color: #FFFFFF !important;
        font-size: 14px !important;
        border: none !important;
        box-shadow: none !important;
        box-sizing: border-box !important;
    }

    .resize-btn:hover:not(:disabled) {
        background-color: #2A00A0 !important;
    }

    .resize-btn:disabled {
        background-color: #cfd4d4 !important;
        color: rgba(0, 0, 0, 0.5) !important;
        cursor: not-allowed !important;
    }

    .resize-btn img {
        width: 20px !important;
        height: 20px !important;
        margin-right: 10px !important;
        opacity: 1 !important;
        transition: none !important;
        filter: brightness(0) invert(1) !important;
        -webkit-transition: none !important;
        -moz-transition: none !important;
        -o-transition: none !important;
    }

    .resize-btn:disabled img {
        opacity: 0.5 !important;
        transition: none !important;
        filter: brightness(0) !important;
        -webkit-transition: none !important;
        -moz-transition: none !important;
        -o-transition: none !important;
    }

    .tab-container {
        margin-bottom: 20px !important;
        display: flex !important;
        width: 100% !important;
    }

    .tab-button {
        flex: 1 !important;
        padding: 8px 16px !important;
        border: 1px solid #470CED !important;
        background-color: #ffffff !important;
        color: #470CED !important;
        cursor: pointer !important;
        outline: none !important;
        margin: 0 !important;
        border-radius: 0 !important;
        text-align: center !important;
        border-right: none !important;
        font-size: 13px !important;
        transition: background-color 0.3s ease, color 0.3s ease !important;
    }

    .tab-button:last-child {
        border-right: 1px solid #470CED !important;
    }

    .tab-button.active {
        background-color: #470CED !important;
        color: #ffffff !important;
    }

    .color-conversion-container {
        margin-top: 20px;
        margin-bottom: 0px;
    }

    .color-conversion-label {
        font-family: "Amazon Ember", sans-serif;
        font-size: 13px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 8px;
        display: block;
    }

    .snap-dropdown {
        position: relative;
        width: 100%;
        cursor: pointer;
        user-select: none;
    }

    .snap-dropdown .dropdown-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 12px;
        height: 40px;
        border: 1.5px solid #DCE0E5;
        border-radius: 4px;
        background: white;
        box-sizing: border-box;
        transition: border-color 0.2s ease;
        outline: none;
    }

    .snap-dropdown.focused .dropdown-header {
        border-color: #470CED;
        outline: none;
        box-shadow: none;
    }

    .snap-dropdown .dropdown-header span {
        font-family: "Amazon Ember";
        font-weight: 500;
        font-size: 12px;
        color: #181818;
        line-height: 40px;
    }

    .snap-dropdown .dropdown-header img {
        width: 16px;
        height: 16px;
    }

    .snap-dropdown .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1.5px solid #DCE0E5;
        border-radius: 4px;
        margin-top: 4px;
        z-index: 1000;
        max-height: 300px;
        overflow-y: auto;
        display: none;
    }

    .snap-dropdown .dropdown-menu.show {
        display: block;
    }

    .snap-dropdown .dropdown-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        font-family: "Amazon Ember";
        font-weight: 500;
        font-size: 12px;
        color: #181818;
        transition: background-color 0.2s ease;
        cursor: pointer;
    }

    .snap-dropdown .dropdown-item:hover {
        background: #F3F4F6;
    }

    .snap-dropdown .dropdown-item.selected {
        font-weight: 700;
        color: #470CED;
    }
`;
document.head.appendChild(styleSheet);

!(function () {
    "use strict";
    !(function () {
        let e = {};
        const t = {},
            n = [
                {
                    name: "TUMBLER",
                    displayName: "Tumbler",
                    containerHeading: "Customize and Re-upload",
                    ctaButtonText: "Apply and Upload",
                    cardId: "TUMBLER-card",
                    editButtonClass: "TUMBLER-edit-btn",
                    resizeContainerClass: "tumbler-resize-container",
                    uploadInputSelector: 'label.file-upload-input[for="TUMBLER-DESIGN-wizzy"]',
                    tabs: ["One Side", "Two Sides", "2 Different Sides"],
                    functions: { "One Side": ["Custom Scale", "100%", "85%", "75%"], "Two Sides": ["Custom Scale", "100%", "85%", "75%", "Pattern"], "2 Different Sides": ["Custom Scale", "100%", "85%", "75%"] },
                    finalCanvasDimensions: { width: 3000, height: 1400 },
                    injectPosition: "second",
                    functionsEnabled: { trimDesign: true, resizeImage: true, uploadFinalImage: true },
                    additionalUploadSteps: false,
                    disableScaleOptions: false,
                    patternSettings: { numberOfRows: 3, designsPerRow: 7 },
                    designDimensionsAndPositioning: { firstRow: { verticalAlignment: "center", snap: "top" }, lastRow: { verticalAlignment: "center", snap: "bottom" }, middleRows: { distribution: "evenly" } },
                    patternStyle: { offset: true },
                    downloadacopy: true,
                    colorConversionEnabled: true,
                },
                {
                    name: "MUG",
                    displayName: "Ceramic Mug",
                    containerHeading: "Customize and Re-upload:",
                    ctaButtonText: "Apply and Upload",
                    cardId: "MUG-card",
                    editButtonClass: "MUG-edit-btn",
                    resizeContainerClass: "mug-resize-container",
                    uploadInputSelector: 'label.file-upload-input[for="MUG-DESIGN-wizzy"]',
                    tabs: ["One Side", "Two Sides", "2 Different Sides"],
                    functions: { "One Side": ["Custom Scale", "100%", "85%", "75%"], "Two Sides": ["Custom Scale", "100%", "85%", "75%", "Pattern"], "2 Different Sides": ["Custom Scale", "100%", "85%", "75%"] },
                    finalCanvasDimensions: { width: 2700, height: 1050 },
                    injectPosition: "second",
                    functionsEnabled: { trimDesign: true, resizeImage: true, uploadFinalImage: true },
                    additionalUploadSteps: false,
                    disableScaleOptions: false,
                    patternSettings: { numberOfRows: 3, designsPerRow: 6 },
                    designDimensionsAndPositioning: { firstRow: { verticalAlignment: "center", snap: "top" }, lastRow: { verticalAlignment: "center", snap: "bottom" }, middleRows: { distribution: "evenly" } },
                    patternStyle: { offset: true },
                    downloadacopy: true,
                    colorConversionEnabled: true,
                },
                {
                    name: "TOTE_BAG",
                    displayName: "Tote Bag",
                    containerHeading: "Customize and Re-upload:",
                    ctaButtonText: "Apply and Upload",
                    cardId: "TOTE_BAG-card",
                    editButtonClass: "TOTE_BAG-edit-btn",
                    resizeContainerClass: "tote-resize-container",
                    uploadInputSelector: 'label.file-upload-input[for="TOTE_BAG-DESIGN-wizzy"]',
                    tabs: ["Standard"],
                    functions: { Standard: ["Custom Scale", "100%", "85%", "75%", "Pattern"] },
                    finalCanvasDimensions: { width: 2925, height: 2925 },
                    injectPosition: "first",
                    functionsEnabled: { trimDesign: true, resizeImage: true, uploadFinalImage: true },
                    additionalUploadSteps: false,
                    disableScaleOptions: false,
                    patternSettings: { numberOfRows: 4, designsPerRow: 4 },
                    designDimensionsAndPositioning: { firstRow: { verticalAlignment: "center", snap: "top" }, lastRow: { verticalAlignment: "center", snap: "bottom" }, middleRows: { distribution: "evenly" } },
                    patternStyle: { offset: true },
                    downloadacopy: true,
                    fillArtwork: true,
                    colorConversionEnabled: true,
                },
                {
                    name: "THROW_PILLOW",
                    displayName: "Throw Pillow",
                    containerHeading: "Customize and Re-upload:",
                    ctaButtonText: "Apply and Upload",
                    cardId: "THROW_PILLOW-card",
                    editButtonClass: "THROW_PILLOW-edit-btn",
                    resizeContainerClass: "throw-resize-container",
                    uploadInputSelector: 'label.file-upload-input[for="THROW_PILLOW-DESIGN-wizzy"]',
                    tabs: ["Standard"],
                    functions: { Standard: ["Custom Scale", "100%", "85%", "75%", "Pattern"] },
                    finalCanvasDimensions: { width: 2925, height: 2925 },
                    injectPosition: "second",
                    functionsEnabled: { trimDesign: true, resizeImage: true, uploadFinalImage: true },
                    additionalUploadSteps: false,
                    disableScaleOptions: false,
                    patternSettings: { numberOfRows: 4, designsPerRow: 4 },
                    designDimensionsAndPositioning: { firstRow: { verticalAlignment: "center", snap: "top" }, lastRow: { verticalAlignment: "center", snap: "bottom" }, middleRows: { distribution: "evenly" } },
                    patternStyle: { offset: true },
                    downloadacopy: true,
                    fillArtwork: true,
                    colorConversionEnabled: true,
                },
                {
                    name: "PHONE_CASE_APPLE_IPHONE",
                    displayName: "iPhone Case",
                    containerHeading: "Customize and Re-upload:",
                    ctaButtonText: "Apply and Upload",
                    cardId: "PHONE_CASE_APPLE_IPHONE-card",
                    editButtonClass: "PHONE_CASE_APPLE_IPHONE-edit-btn",
                    resizeContainerClass: "iphone-resize-container",
                    uploadInputSelector: 'label.file-upload-input[for="PHONE_CASE_APPLE_IPHONE-DESIGN-wizzy"]',
                    tabs: ["Standard"],
                    functions: { Standard: ["Custom Scale", "100%", "85%", "75%", "Pattern"] },
                    finalCanvasDimensions: { width: 1800, height: 3200 },
                    injectPosition: "second",
                    functionsEnabled: { trimDesign: true, resizeImage: true, uploadFinalImage: true },
                    additionalUploadSteps: false,
                    disableScaleOptions: false,
                    patternSettings: { numberOfRows: 8, designsPerRow: 4 },
                    designDimensionsAndPositioning: { firstRow: { verticalAlignment: "center", snap: "top" }, lastRow: { verticalAlignment: "center", snap: "bottom" }, middleRows: { distribution: "evenly" } },
                    patternStyle: { offset: true },
                    downloadacopy: true,
                    fillArtwork: true,
                    colorConversionEnabled: true,
                },
                {
                    name: "POP_SOCKET",
                    displayName: "PopSockets Grip",
                    containerHeading: "Customize and Re-upload:",
                    ctaButtonText: "Apply and Upload",
                    cardId: "POP_SOCKET-card",
                    editButtonClass: "POP_SOCKET-edit-btn",
                    resizeContainerClass: "popsockets-resize-container",
                    uploadInputSelector: 'label.file-upload-input[for="POP_SOCKET-DESIGN-wizzy"]',
                    tabs: ["Standard"],
                    functions: { Standard: ["Custom Scale", "100%", "85%", "75%", "Pattern"] },
                    finalCanvasDimensions: { width: 1200, height: 1200 },
                    injectPosition: "second",
                    functionsEnabled: { trimDesign: true, resizeImage: true, uploadFinalImage: true },
                    additionalUploadSteps: false,
                    disableScaleOptions: false,
                    patternSettings: { numberOfRows: 3, designsPerRow: 3 },
                    designDimensionsAndPositioning: { firstRow: { verticalAlignment: "center", snap: "top" }, lastRow: { verticalAlignment: "center", snap: "bottom" }, middleRows: { distribution: "evenly" } },
                    patternStyle: { offset: true },
                    downloadacopy: true,
                    fillArtwork: true,
                    colorConversionEnabled: true,
                },
            ];
        function a() {
            const t = JSON.parse(JSON.stringify(e));
            for (const e in t) {
                const n = t[e];
                if (n.tabs)
                    for (const e in n.tabs) {
                        const t = n.tabs[e];
                        t.leftImageData && delete t.leftImageData;
                        t.rightImageData && delete t.rightImageData;
                    }
            }
            localStorage.setItem("accessoriesResizePreferences", JSON.stringify(t));
        }
        function i(i, g) {
            const wrapper = g.closest(".mb-base");
            const skipContainers = [
                ".tote-resize-container",
                ".throw-resize-container",
                ".iphone-resize-container",
                ".popsockets-resize-container",
                ".mug-resize-container"
            ];
            if (i.name === "ZIP_HOODIE") {
                skipContainers.forEach(sel => {
                    const found = wrapper.querySelector(sel);
                    if (found) found.remove();
                });
                return;
            }

            if (t[i.name]) return;
            if (g.querySelector(`.${i.resizeContainerClass}`)) {
                t[i.name] = false;
                return;
            }

            if (!e[i.name]) {
                let defaultTab = i.tabs[0];
                if ("TUMBLER" === i.name) defaultTab = "Two Sides";
                e[i.name] = { selectedTab: defaultTab, tabs: {}, downloadaCopy: false };
                i.tabs.forEach((tabName) => {
                    let defaultScaling = "percent85";
                    if ("POP_SOCKET" === i.name) defaultScaling = "percent100";
                    e[i.name].tabs[tabName] = {
                        scalingOption: defaultScaling,
                        customScaleValue: "",
                        ...("2 Different Sides" === tabName && {
                            leftImageUploaded: false,
                            rightImageUploaded: false,
                            leftImageData: null,
                            rightImageData: null,
                        }),
                    };
                });
                if (void 0 !== i.fillArtwork) e[i.name].fillArtwork = i.fillArtwork;
                a();
            }
            !(function (e, t) {
                n.forEach((n) => {
                    if (n.name !== e.name) {
                        const e = t.querySelector(`.${n.resizeContainerClass}`);
                        e && e.remove();
                    }
                });
            })(i, g);

            const S = (function (t) {
                const n = document.createElement("div");
                (n.className = t.resizeContainerClass),
                    (n.style.backgroundColor = "#ffffff"),
                    (n.style.border = "0.0625rem solid #d5dbdb"),
                    (n.style.marginTop = "10px"),
                    (n.style.borderRadius = "0px"),
                    (n.style.boxSizing = "border-box"),
                    (n.style.fontFamily = '"Amazon Ember", sans-serif'),
                    (n.style.padding = "15px");
                const i = document.createElement("div");
                (i.textContent = t.containerHeading),
                    (i.style.fontSize = "13px"),
                    (i.style.fontWeight = "600"),
                    (i.style.color = "#333333"),
                    (i.style.backgroundColor = "#fafafa"),
                    (i.style.borderBottom = "0.0625rem solid #efefef"),
                    (i.style.padding = "7.5px 15px"),
                    (i.style.margin = "-15px -15px 15px -15px"),
                    (i.style.width = "auto"),
                    n.appendChild(i);
                if (t.tabs.length > 1) {
                    const i = document.createElement("div");
                    i.className = "tab-container";
                    t.tabs.forEach((d) => {
                        const c = document.createElement("button");
                        c.textContent = d;
                        c.className = "tab-button";
                        if (e[t.name] && e[t.name].selectedTab === d) {
                            c.classList.add("active");
                        }
                        c.dataset.tabName = d;
                        c.addEventListener("click", async function () {
                            const c = e[t.name];
                            if (!c) return;
                            if (c.selectedTab === d) return;
                            c.selectedTab = d;
                            a();
                            i.querySelectorAll(".tab-button").forEach(btn => {
                                if (btn.dataset.tabName === d) {
                                    btn.classList.add("active");
                                } else {
                                    btn.classList.remove("active");
                                }
                            });
                            const p = n.querySelector(".scale-options-container");
                            t.disableScaleOptions || (l(t, p, n), p.style.display = "block");
                            r(t, n);
                            await h(300);
                            o(t, n);
                            if ("2 Different Sides" === d) {
                                b(n, t);
                            } else {
                                C(n);
                            }
                            if (!c.tabs[d]) {
                                c.tabs[d] = {
                                    scalingOption: "percent85",
                                    customScaleValue: "",
                                    colorConversion: "original"
                                };
                                a();
                            }
                            if (t.colorConversionEnabled) {
                                const colorConversionContainer = n.querySelector(".color-conversion-container");
                                if (colorConversionContainer) {
                                    const hasAllowedTabs = t.tabs && (t.tabs.includes("One Side") || t.tabs.includes("Two Sides"));
                                    if (hasAllowedTabs) {
                                        const isAllowedTab = d === "One Side" || d === "Two Sides";
                                        if (isAllowedTab) {
                                            colorConversionContainer.style.display = "block";
                                            const dropdown = colorConversionContainer.querySelector(".snap-dropdown");
                                            const headerSpan = dropdown?.querySelector(".dropdown-header span");
                                            const currentConversion = c.tabs[d]?.colorConversion || "original";
                                            const conversionLabels = {
                                                "original": "Original",
                                                "invert": "Invert to 100% Black",
                                                "smart": "Smart Invert to Black",
                                                "blackBackground": "Black Outline"
                                            };
                                            if (headerSpan) headerSpan.textContent = conversionLabels[currentConversion] || "Original";
                                            dropdown?.querySelectorAll(".dropdown-item").forEach(item => {
                                                item.classList.remove("selected");
                                                if (item.dataset.value === currentConversion) {
                                                    item.classList.add("selected");
                                                }
                                            });
                                        } else {
                                            colorConversionContainer.style.display = "none";
                                        }
                                    } else {
                                        colorConversionContainer.style.display = "block";
                                    }
                                }
                            }
                            l(t, p, n);
                        });
                        i.appendChild(c);
                    });
                    n.appendChild(i);
                } else {
                    if (e[t.name]) {
                        e[t.name].selectedTab = t.tabs[0];
                        a();
                    }
                }
                let colorConversionContainer = null;
                if (t.colorConversionEnabled) {
                    colorConversionContainer = (function(t, n) {
                        const container = document.createElement("div");
                        container.className = "color-conversion-container";
                        const selectedTab = e[t.name]?.selectedTab;
                        const hasAllowedTabs = t.tabs && (t.tabs.includes("One Side") || t.tabs.includes("Two Sides"));
                        if (hasAllowedTabs) {
                            const isAllowedTab = selectedTab && (selectedTab === "One Side" || selectedTab === "Two Sides");
                            container.style.display = isAllowedTab ? "block" : "none";
                        } else {
                            container.style.display = "block";
                        }

                        const label = document.createElement("label");
                    label.className = "color-conversion-label";
                    label.style.display = "flex";
                    label.style.alignItems = "center";
                    label.style.gap = "0";
                    label.textContent = "Color Conversion:";

                    const hintContainer = document.createElement("div");
                    hintContainer.style.display = "flex";
                    hintContainer.style.alignItems = "center";
                    hintContainer.style.marginLeft = "8px";
                    hintContainer.style.fontSize = "12px";
                    hintContainer.style.color = "#666";

                    const hintIcon = document.createElement("img");
                    hintIcon.src = chrome.runtime.getURL("assets/tip-ic.svg");
                    hintIcon.style.width = "12px";
                    hintIcon.style.height = "12px";
                    hintIcon.style.marginRight = "5px";
                    hintIcon.style.cursor = "help";

                    const hintText = document.createElement("span");
                    hintText.textContent = "Text and silhouette graphics only";

                    hintContainer.appendChild(hintIcon);
                    hintContainer.appendChild(hintText);
                    label.appendChild(hintContainer);
                    container.appendChild(label);

                    const dropdown = document.createElement("div");
                    dropdown.className = "snap-dropdown";

                        const header = document.createElement("div");
                        header.className = "dropdown-header";

                        const headerSpan = document.createElement("span");
                        const tabForConversion = e[t.name]?.selectedTab;
                        const currentConversion = e[t.name]?.tabs[tabForConversion]?.colorConversion || "original";
                    const conversionLabels = {
                        "original": "Original",
                        "invert": "Invert to 100% Black",
                        "smart": "Smart Invert to Black",
                        "blackBackground": "Black Outline"
                    };
                    headerSpan.textContent = conversionLabels[currentConversion] || "Original";
                    header.appendChild(headerSpan);

                    const dropdownIcon = document.createElement("img");
                    dropdownIcon.src = chrome.runtime.getURL("assets/dropdown-ic.svg");
                    dropdownIcon.alt = "Dropdown";
                    header.appendChild(dropdownIcon);

                    const menu = document.createElement("div");
                    menu.className = "dropdown-menu";

                    const dropdownList = document.createElement("div");
                    dropdownList.className = "dropdown-list";

                    const options = [
                        { value: "original", label: "Original" },
                        { value: "invert", label: "Invert to 100% Black" },
                        { value: "smart", label: "Smart Invert to Black" },
                        { value: "blackBackground", label: "Black Outline" }
                    ];

                    options.forEach(option => {
                        const item = document.createElement("div");
                        item.className = "dropdown-item";
                        if (option.value === currentConversion) {
                            item.classList.add("selected");
                        }
                        item.textContent = option.label;
                        item.dataset.value = option.value;

                        item.addEventListener("click", function(event) {
                            event.stopPropagation();

                            if (item.classList.contains("selected")) {
                                menu.classList.remove("show");
                                dropdown.classList.remove("focused");
                                return;
                            }

                            dropdownList.querySelectorAll(".dropdown-item").forEach(i => i.classList.remove("selected"));
                            item.classList.add("selected");
                            headerSpan.textContent = option.label;

                            const currentTabState = e[t.name]?.selectedTab;
                            if (currentTabState && e[t.name]?.tabs[currentTabState]) {
                                e[t.name].tabs[currentTabState].colorConversion = option.value;
                                a();
                            }

                            menu.classList.remove("show");
                            dropdown.classList.remove("focused");
                        });

                        dropdownList.appendChild(item);
                    });

                    menu.appendChild(dropdownList);
                    dropdown.appendChild(header);
                    dropdown.appendChild(menu);

                    header.addEventListener("click", function(event) {
                        event.stopPropagation();
                        const isOpen = menu.classList.contains("show");

                        document.querySelectorAll(".snap-dropdown .dropdown-menu.show").forEach(m => {
                            if (m !== menu) {
                                m.classList.remove("show");
                                m.closest(".snap-dropdown").classList.remove("focused");
                            }
                        });

                        menu.classList.toggle("show");
                        dropdown.classList.toggle("focused");
                    });

                    document.addEventListener("click", function(event) {
                        if (!dropdown.contains(event.target)) {
                            menu.classList.remove("show");
                            dropdown.classList.remove("focused");
                        }
                    });

                        container.appendChild(dropdown);
                        return container;
                    })(t, n);
                }
                const g = document.createElement("div");
                (g.className = "scale-options-container"), (g.style.marginTop = "20px"), (g.style.marginBottom = "0px");
                const S = document.createElement("form");
                S.className = "resize-form";
                const w = document.createElement("div");
                (w.style.marginBottom = "20px"), (w.className = "radio-container"), S.appendChild(w), g.appendChild(S);
                if (colorConversionContainer) {
                    n.appendChild(colorConversionContainer);
                }
                n.appendChild(g);
                if (t.downloadacopy) {
                    const i = document.createElement("div");
                    i.style.marginTop = "16px";
                    i.style.display = "flex";
                    i.style.alignItems = "center";
                    i.style.justifyContent = "flex-start";
                    i.style.cursor = "pointer";
                    i.style.width = "100%";

                    const toggleContainer = document.createElement("div");
                    toggleContainer.style.width = "36px";
                    toggleContainer.style.height = "18px";
                    toggleContainer.style.borderRadius = "18px";
                    toggleContainer.style.backgroundColor = (e[t.name] && e[t.name].downloadaCopy) ? "#470CED" : "rgb(207, 212, 212)";
                    toggleContainer.style.position = "relative";
                    toggleContainer.style.transition = "background-color 0.3s";
                    toggleContainer.style.marginRight = "10px";

                    const toggleHandle = document.createElement("span");
                    toggleHandle.style.width = "12px";
                    toggleHandle.style.height = "12px";
                    toggleHandle.style.borderRadius = "50%";
                    toggleHandle.style.backgroundColor = "white";
                    toggleHandle.style.position = "absolute";
                    toggleHandle.style.top = "3px";
                    toggleHandle.style.left = "3px";
                    toggleHandle.style.transition = "transform 0.3s";
                    toggleHandle.style.transform = (e[t.name] && e[t.name].downloadaCopy) ? "translateX(18px)" : "translateX(0)";

                    toggleContainer.appendChild(toggleHandle);

                    const l = document.createElement("span");
                    l.textContent = "Download a copy";
                    l.style.fontSize = "14px";
                    l.style.color = "rgb(100, 100, 100)";
                    l.style.userSelect = "none";

                    i.addEventListener("click", function() {
                        if (!e[t.name]) return;
                        const isActive = !e[t.name].downloadaCopy;
                        e[t.name].downloadaCopy = isActive;
                        toggleContainer.style.backgroundColor = isActive ? "#470CED" : "rgb(207, 212, 212)";
                        toggleHandle.style.transform = isActive ? "translateX(18px)" : "translateX(0)";
                        a();
                    });

                    i.appendChild(toggleContainer);
                    i.appendChild(l);
                    n.appendChild(i);
                }
                const I = document.createElement("div");
                (I.className = "apply-btn-container"), (I.style.textAlign = "left"), (I.style.marginTop = "10px"), (I.style.marginBottom = "0px");
                const T = document.createElement("button");
                (T.type = "button"),
                    (T.className = "resize-btn"),
                    (T.disabled = false),
                    (T.title = "Click to apply the resizing and upload the design.");
                const D = document.createElement("img");
                (D.src = chrome.runtime.getURL("assets/apply.svg")),
                    (D.alt = "Apply and Upload Icon"),
                    (D.style.width = "20px"),
                    (D.style.height = "20px"),
                    (D.style.marginRight = "10px");
                const v = document.createElement("span");
                function A(e) {
                    e.disabled = !!e.disabled;
                }
                (v.textContent = t.ctaButtonText),
                    T.appendChild(D),
                    T.appendChild(v),
                    A(T),
                    T.addEventListener("click", async () => {
                        const n = T,
                            a = T.closest(`.${t.resizeContainerClass}`);
                        (n.disabled = true), (n.style.cursor = "not-allowed"), A(n);
                        const i = (function (t, n) {
                            const a = n.querySelector('input[name="resizeOption"]:checked'),
                                i = e[t.name];
                            if (!i) return 1;
                            const o = i.selectedTab;
                            i.tabs[o];
                            if (a)
                                switch (a.id) {
                                    case "custom":
                                        const e = n.querySelector('input[type="number"]'),
                                            t = parseFloat(e.value);
                                        return t >= 50 && t <= 100 ? t / 100 : null;
                                    case "percent100":
                                    default:
                                        return 1;
                                    case "percent85":
                                        return 0.85;
                                    case "percent75":
                                        return 0.75;
                                    case "pattern":
                                        return "pattern";
                                }
                            return 1;
                        })(t, a);
                        if (null === i) return await h(300), (n.disabled = false), (n.style.cursor = "pointer"), void A(n);
                        const o = await (async function (t, n) {
                            try {
                                let a;
                                if ("pattern" === t && n.patternSettings)
                                    a = await (async function (productConfig) {
                                        try {
                                            const t = await c();
                                            if (!t) return null;
                                            const { trimDesign: n, resizeImage: a } = productConfig.functionsEnabled;
                                            let i = t;
                                            if (n || a) {
                                                n && (i = await p(t));
                                                if (productConfig.colorConversionEnabled) {
                                                    const currentTab = e[productConfig.name]?.selectedTab;
                                                    const colorConversion = e[productConfig.name]?.tabs?.[currentTab]?.colorConversion || "original";
                                                    if (colorConversion === "invert") {
                                                        i = await invertToBlack(i);
                                                    } else if (colorConversion === "smart") {
                                                        i = await smartInvert(i);
                                                    } else if (colorConversion === "blackBackground") {
                                                        i = await blackBackground(i);
                                                    }
                                                }
                                                a && (i = await u(i, productConfig, 1));
                                                if (n || a) i = await m(i, productConfig);
                                            }
                                            const o = await f(i, 300);
                                            return await y(o, productConfig), o;
                                        } catch (err) {
                                            return null;
                                        }
                                    })(n);
                                else if ("TUMBLER" === n.name && "2 Different Sides" === e[n.name].selectedTab)
                                    a = await (async function (t, n) {
                                        try {
                                            const a = e[t.name].tabs["2 Different Sides"],
                                                i = a.leftImageData,
                                                o = a.rightImageData;
                                            if (!i || !o) return null;
                                            let s = await E(i, t, n),
                                                l = await E(o, t, n);
                                            const r = document.createElement("canvas");
                                            (r.width = t.finalCanvasDimensions.width), (r.height = t.finalCanvasDimensions.height);
                                            const c = r.getContext("2d");
                                            c.clearRect(0, 0, r.width, r.height);
                                            const p = 31,
                                                u = 135.6667,
                                                m = 1400,
                                                g = 1400,
                                                y = (t.finalCanvasDimensions.height - g) / 2;
                                            d(c, s, p, y, m, g);
                                            d(c, l, p + m + u, y, m, g);
                                            return await f(r, 300);
                                        } catch (e) {
                                            return null;
                                        }
                                    })(n, t);
                                else if ("MUG" === n.name && "2 Different Sides" === e[n.name].selectedTab)
                                    a = await (async function (t, n) {
                                        try {
                                            const a = e[t.name].tabs["2 Different Sides"],
                                                i = a.leftImageData,
                                                o = a.rightImageData;
                                            if (!i || !o) return null;
                                            let s = await E(i, t, n),
                                                l = await E(o, t, n);
                                            const r = document.createElement("canvas");
                                            (r.width = t.finalCanvasDimensions.width), (r.height = t.finalCanvasDimensions.height);
                                            const c = r.getContext("2d");
                                            c.clearRect(0, 0, r.width, r.height);
                                            const p = 59,
                                                u = 482,
                                                m = 1050,
                                                g = 1045.646,
                                                y = (t.finalCanvasDimensions.height - g) / 2;
                                            d(c, s, p, y, m, g);
                                            d(c, l, p + m + u, y, m, g);
                                            return await f(r, 300);
                                        } catch (e) {
                                            return null;
                                        }
                                    })(n, t);
                                else {
                                    const i = await c();
                                    if (!i) return null;
                                    const { trimDesign: o, resizeImage: s } = n.functionsEnabled;
                                    let l = i;
                                    if (o || s) {
                                        o && (l = await p(i));
                                        if (n.colorConversionEnabled) {
                                            const currentTab = e[n.name]?.selectedTab;
                                            const colorConversion = e[n.name]?.tabs[currentTab]?.colorConversion || "original";
                                            if (colorConversion === "invert") {
                                                l = await invertToBlack(l);
                                            } else if (colorConversion === "smart") {
                                                l = await smartInvert(l);
                                            } else if (colorConversion === "blackBackground") {
                                                l = await blackBackground(l);
                                            }
                                        }
                                        s && (l = await u(l, n, t));
                                        if (o || s) {
                                            l = await (function (t, n, a) {
                                                if ("pattern" === a && n.patternSettings) return m(t, n);
                                                return new Promise((a) => {
                                                    const i = document.createElement("canvas");
                                                    (i.width = n.finalCanvasDimensions.width), (i.height = n.finalCanvasDimensions.height);
                                                    const o = i.getContext("2d");
                                                    o.clearRect(0, 0, i.width, i.height),
                                                        "TUMBLER" === n.name
                                                            ? (function (t, n, a) {
                                                                  const i = 31,
                                                                      o = 135.6667,
                                                                      s = 1400,
                                                                      l = 1400,
                                                                      r = e[a.name].selectedTab;
                                                                  if ("Two Sides" === r) {
                                                                      const e = (a.finalCanvasDimensions.height - l) / 2;
                                                                      d(t, n, i, e, s, l);
                                                                      d(t, n, i + s + o, e, s, l);
                                                                  } else if ("One Side" === r) {
                                                                      d(t, n, i, (a.finalCanvasDimensions.height - l) / 2, s, l);
                                                                  }
                                                              })(o, t, n)
                                                            : "MUG" === n.name
                                                            ? (function (t, n, a) {
                                                                  const i = 59,           // LEFT PADDING (59px)
                                                                      o = 482,          // MIDDLE GAP (482px)
                                                                      s = 1050,         // DESIGN WIDTH (1050px)
                                                                      l = 1045.646,     // DESIGN HEIGHT (1045.646px)
                                                                      r = e[a.name].selectedTab;
                                                                  if ("Two Sides" === r) {
                                                                      const e = (a.finalCanvasDimensions.height - l) / 2;
                                                                      d(t, n, i, e, s, l);
                                                                      d(t, n, i + s + o, e, s, l);
                                                                  } else if ("One Side" === r) {
                                                                      d(t, n, i, (a.finalCanvasDimensions.height - l) / 2, s, l);
                                                                  }
                                                              })(o, t, n)
                                                            : (function (e, t, n) {
                                                                  const a = n.finalCanvasDimensions.width,
                                                                      i = n.finalCanvasDimensions.height;
                                                                  let o, s;
                                                                  if (t instanceof HTMLCanvasElement) {
                                                                      o = t.width;
                                                                      s = t.height;
                                                                  } else {
                                                                      const e = new Image();
                                                                      (e.src = t), (o = e.width), (s = e.height);
                                                                  }
                                                                  const l = (a - o) / 2,
                                                                      r = (i - s) / 2;
                                                                  e.drawImage(t, l, r, o, s);
                                                              })(o, t, n),
                                                        a(i);
                                                });
                                            })(l, n, t);
                                        }
                                    }
                                    a = await f(l, 300);
                                }
                                return await y(a, n), a;
                            } catch (e) {
                                return null;
                            }
                        })(i, t);
                        if (e[t.name] && e[t.name].downloadaCopy && o) {
                            const e = URL.createObjectURL(o),
                                n = document.createElement("a");
                            (n.href = e), (n.download = `${t.name}_design_copy.png`), document.body.appendChild(n), n.click(), document.body.removeChild(n), URL.revokeObjectURL(e);
                        }
                        if (t.fillArtwork)
                            try {
                                await (function (e, t = 5000) {
                                    return new Promise((n, a) => {
                                        const i = document.querySelector(e);
                                        if (i) return void n(i);
                                        const o = new MutationObserver((t) => {
                                            const a = document.querySelector(e);
                                            a && (n(a), o.disconnect());
                                        });
                                        o.observe(document.body, { childList: true, subtree: true }),
                                            setTimeout(() => {
                                                o.disconnect(), a(new Error(`Element with selector "${e}" not found within ${t}ms.`));
                                            }, t);
                                    });
                                })(".product-editor #fill-artwork-btn", 60000);
                                const e = document.querySelector(".product-editor #fill-artwork-btn");
                                e && e.click();
                            } catch (e) {}
                        await h(300), (n.disabled = false), (n.style.cursor = "pointer"), A(n);
                    }),
                    I.appendChild(T),
                    n.appendChild(I),
                    t.disableScaleOptions || l(t, g, n);
                r(t, n);
                const x = e[t.name];
                if (x && "2 Different Sides" === x.selectedTab) b(n, t);
                return n;
            })(i);
            S.setAttribute("data-resize-container", i.name);
            let w = null;
            switch (i.injectPosition) {
                case "first":
                    (w = g.firstChild), g.insertBefore(S, w);
                    break;
                case "second":
                    (w = g.children[1]), w ? g.insertBefore(S, w) : g.appendChild(S);
                    break;
                case "third":
                    (w = g.children[2]), w ? g.insertBefore(S, w) : g.appendChild(S);
                    break;
                default:
                    g.appendChild(S);
            }
            const I = S.querySelector(".scale-options-container");
            i.disableScaleOptions ? (I.style.display = "none") : l(i, I, S);
            r(i, S);
            o(i, S);
            t[i.name] = false;
        }
        function o(t, n) {
            if (!n.querySelector(".resize-btn")) return;
            r(t, n);
            const a = e[t.name] ? e[t.name].selectedTab : null;
            if (!a) return;
            if (("TUMBLER" === t.name || "MUG" === t.name) && "2 Different Sides" === a) {
                const e = n.querySelector(".upload-areas-container");
                if (e) {
                    const a = { attributes: true, childList: true, subtree: true };
                    new MutationObserver(() => {
                        r(t, n);
                    }).observe(e, a);
                }
            } else {
                const e = n.closest(".product-editor");
                if (!e) return;
                const a = { childList: true, subtree: true };
                new MutationObserver(() => {
                    r(t, n);
                }).observe(e, a);
            }
        }
        function s(t, n) {
            const a = e[t.name];
            if (!a) return;
            const i = n.querySelectorAll(".tab-button");
            i.forEach((e) => {
                e.style.flex = "1";
                e.style.padding = "8px 16px";
                e.style.border = "1px solid #470CED";
                e.style.backgroundColor = e.dataset.tabName === a.selectedTab ? "#470CED" : "#ffffff";
                e.style.color = e.dataset.tabName === a.selectedTab ? "#ffffff" : "#470CED";
                e.style.cursor = "pointer";
                e.style.outline = "none";
                e.style.margin = "0";
                e.style.borderRadius = "0";
                e.style.textAlign = "center";
                e.style.borderRight = "none";
                e.style.transition = "all 0.2s ease";
            });
            i.length > 0 && (i[i.length - 1].style.borderRight = "1px solid #470CED");
        }
        function l(t, n, i) {
            const o = n.querySelector(".radio-container");
            for (; o.firstChild; ) o.removeChild(o.firstChild);
            const s = e[t.name];
            if (!s) return;
            const l = s.selectedTab,
                d = s.tabs[l];
            let c = null;
            const p = (function (t) {
                const n = e[t.name];
                if (!n) return [];
                const a = n.selectedTab,
                    i = t.functions[a];
                const o = i
                    .map((e) => {
                        switch (e) {
                            case "Custom Scale":
                                return { id: "custom", label: "" };
                            case "100%":
                                return { id: "percent100", label: "100%" };
                            case "85%":
                                return { id: "percent85", label: "85%" };
                            case "75%":
                                return { id: "percent75", label: "75%" };
                            case "Pattern":
                                return { id: "pattern", label: "Pattern" };
                            default:
                                return null;
                        }
                    })
                    .filter((e) => null !== e);
                return o;
            })(t);
            p.forEach((e) => {
                const s = document.createElement("label");
                (s.className = "radio-label"),
                    (s.style.marginRight = "15px"),
                    (s.style.fontSize = "13px"),
                    (s.style.display = "inline-flex"),
                    (s.style.alignItems = "center"),
                    (s.style.lineHeight = "1.5rem"),
                    (s.style.position = "relative"),
                    (s.style.cursor = "pointer");
                const l = document.createElement("input");
                (l.type = "radio"),
                    (l.name = "resizeOption"),
                    (l.id = e.id),
                    (l.value = e.label),
                    (l.style.opacity = "0"),
                    (l.style.position = "absolute"),
                    (l.style.left = "0"),
                    (l.style.top = "0"),
                    (l.style.width = "100%"),
                    (l.style.height = "100%"),
                    (l.style.cursor = "pointer");
                const p = document.createElement("span");
                (p.className = "checkmark"),
                    (p.style.display = "inline-block"),
                    (p.style.width = "1rem"),
                    (p.style.height = "1rem"),
                    (p.style.border = "0.0625rem solid #470CED"),
                    (p.style.borderRadius = "50%"),
                    (p.style.backgroundColor = "#ffffff"),
                    (p.style.marginRight = "5px"),
                    (p.style.position = "relative");
                const u = document.createElement("span");
                (u.style.height = "0.5rem"),
                    (u.style.width = "0.5rem"),
                    (u.style.backgroundColor = "#ffffff"),
                    (u.style.borderRadius = "50%"),
                    (u.style.position = "absolute"),
                    (u.style.top = "50%"),
                    (u.style.left = "50%"),
                    (u.style.transform = "translate(-50%, -50%)"),
                    p.appendChild(u);
                l.addEventListener("change", async function () {
                    n.querySelectorAll(".radio-label input").forEach((e) => {
                        const t = e.parentElement.querySelector(".checkmark");
                        (t.style.backgroundColor = "#ffffff"), (t.querySelector("span").style.backgroundColor = "#ffffff");
                    }),
                        l.checked && ((p.style.backgroundColor = "#470CED"), (u.style.backgroundColor = "#ffffff"), (d.scalingOption = e.id), a());
                    if (l.checked && "custom" === e.id) {
                        c && ((c.disabled = false), c.focus());
                    } else if (c) {
                        c.disabled = true;
                        c.value = "";
                        c.style.borderBottom = "2px solid #ccc";
                        d.customScaleValue = "";
                    }
                    r(t, i);
                    await h(300);
                });
                if (d.scalingOption === e.id) {
                    (l.checked = true), (p.style.backgroundColor = "#470CED"), (u.style.backgroundColor = "#ffffff");
                }
                s.appendChild(l);
                s.appendChild(p);
                if ("custom" === e.id) {
                    c = document.createElement("input");
                    (c.type = "number"),
                        (c.min = "50"),
                        (c.max = "100"),
                        (c.step = "0.1"),
                        (c.style.width = "64px"),
                        (c.style.height = "calc(1.5rem * 1.1)"),
                        (c.style.marginLeft = "5px"),
                        (c.style.fontSize = "13px"),
                        (c.style.padding = "2px"),
                        (c.style.border = "none"),
                        (c.style.borderBottom = "2px solid #ccc"),
                        (c.style.borderRadius = "0"),
                        (c.disabled = "custom" !== d.scalingOption),
                        (c.style.transition = "border-bottom 0.3s ease"),
                        (c.value = d.customScaleValue || ""),
                        c.addEventListener("input", async function () {
                            const e = parseFloat(c.value);
                            if (e < 50 || e > 100 || isNaN(e)) {
                                c.style.borderBottom = "2px solid red";
                            } else {
                                c.style.borderBottom = "2px solid #ccc";
                                d.customScaleValue = c.value;
                                a();
                            }
                            r(t, i);
                            await h(300);
                        }),
                        c.addEventListener("blur", async function () {
                            const e = parseFloat(c.value);
                            if (isNaN(e) || e < 50 || e > 100) {
                                c.style.borderBottom = "2px solid red";
                            } else {
                                c.style.borderBottom = "2px solid #ccc";
                            }
                            await h(300);
                        }),
                        s.appendChild(c);
                    const x = document.createElement("span");
                    (x.textContent = " %"), (x.style.marginLeft = "8px"), s.appendChild(x);
                } else {
                    s.appendChild(document.createTextNode(e.label));
                }
                (s.style.paddingLeft = "0"), o.appendChild(s);
            });
            if (c && e[t.name]) {
                const n = e[t.name].selectedTab,
                    a = e[t.name].tabs[n];
                if (a) c.value = a.customScaleValue || "";
            }
            r(t, i);
        }
        function r(t, n) {
            if (!n) return;
            const a = n.querySelector(".resize-btn");
            if (a) {
                let i = true;
                const o = e[t.name];
                if (!o) return;
                const s = o.selectedTab,
                    l = o.tabs[s],
                    r = n.querySelector('input[name="resizeOption"]:checked');
                if (r && "custom" === r.id) {
                    const e = n.querySelector('input[type="number"]'),
                        t = parseFloat(e.value);
                    if (isNaN(t) || t < 50 || t > 100) i = false;
                }
                if (r && "pattern" === r.id) {
                    if (t.functions[s].includes("Pattern")) {
                        if (!t.patternSettings || !t.designDimensionsAndPositioning || !t.patternStyle) i = false;
                    } else {
                        i = false;
                    }
                }
                if (("TUMBLER" === t.name || "MUG" === t.name) && "2 Different Sides" === s) {
                    const e = l.leftImageUploaded,
                        t = l.rightImageUploaded;
                    if (!e || !t) i = false;
                } else {
                    const e = n.closest(".product-editor");
                    if (e) {
                        i = !!e.querySelector(".delete-button");
                    } else i = false;
                }
                (a.disabled = !i), (a.style.cursor = i ? "pointer" : "not-allowed");
                if (a.disabled) {
                    a.style.backgroundImage = "none";
                    a.style.backgroundColor = "#cfd4d4";
                    a.style.color = "#000000";
                    const e = a.querySelector("img");
                    if (e) e.style.filter = "brightness(0)";
                } else {
                    a.style.backgroundColor = "#470CED";
                    a.style.color = "#ffffff";
                    const e = a.querySelector("img");
                    if (e) e.style.filter = "brightness(100)";
                }
            }
        }
        function d(e, t, n, a, i, o) {
            const s = t.width,
                l = t.height,
                r = Math.min(i / s, o / l, 1),
                d = s * r,
                c = l * r,
                p = n + (i - d) / 2,
                u = a + (o - c) / 2;
            e.drawImage(t, p, u, d, c);
        }
        async function c() {
            return new Promise((e, t) => {
                const n = document.querySelectorAll('img[alt$=".png"], img[alt="null"]');
                if (0 === n.length) return void t("Image element not found.");
                let a = Array.from(n).find((e) => e.getAttribute("alt").endsWith(".png"));
                if (!a) a = Array.from(n).find((e) => "null" === e.getAttribute("alt"));
                if (!a) return void t("Image element not found.");
                const i = a.getAttribute("src");
                if (!i) return void t("Image src attribute is missing.");
                const o = new Image();
                (o.crossOrigin = "Anonymous"),
                    (o.src = i),
                    (o.onload = () => {
                        try {
                            const t = document.createElement("canvas");
                            (t.width = o.width), (t.height = o.height);
                            t.getContext("2d", { willReadFrequently: true }).drawImage(o, 0, 0);
                            const n = t.toDataURL();
                            e(n);
                        } catch (e) {
                            t(e);
                        }
                    }),
                    (o.onerror = (e) => {
                        t(e);
                    });
            });
        }
        async function p(e) {
            return new Promise((t, n) => {
                const a = new Image();
                (a.src = e),
                    (a.onload = () => {
                        const e = document.createElement("canvas");
                        (e.width = a.width), (e.height = a.height);
                        const n = e.getContext("2d", { willReadFrequently: true });
                        n.drawImage(a, 0, 0);
                        const i = n.getImageData(0, 0, a.width, a.height),
                            { topTrim: o, bottomTrim: s, leftTrim: l, rightTrim: r } = (function (e) {
                                const t = e.data;
                                let n = 0,
                                    a = e.height,
                                    i = 0,
                                    o = e.width;
                                for (let a = 0; a < e.height; a++) {
                                    for (let i = 0; i < e.width; i++) {
                                        if (0 !== t[4 * (a * e.width + i) + 3]) {
                                            n = a;
                                            break;
                                        }
                                    }
                                    if (0 !== n) break;
                                }
                                for (let i = e.height - 1; i >= n; i--) {
                                    for (let n = 0; n < e.width; n++) {
                                        if (0 !== t[4 * (i * e.width + n) + 3]) {
                                            a = i;
                                            break;
                                        }
                                    }
                                    if (a !== e.height) break;
                                }
                                for (let o = 0; o < e.width; o++) {
                                    for (let s = n; s <= a; s++) {
                                        if (0 !== t[4 * (s * e.width + o) + 3]) {
                                            i = o;
                                            break;
                                        }
                                    }
                                    if (0 !== i) break;
                                }
                                for (let s = e.width - 1; s >= i; s--) {
                                    for (let i = n; i <= a; i++) {
                                        if (0 !== t[4 * (i * e.width + s) + 3]) {
                                            o = s;
                                            break;
                                        }
                                    }
                                    if (o !== e.width) break;
                                }
                                if (n >= a || i >= o) return { topTrim: 0, bottomTrim: e.height, leftTrim: 0, rightTrim: e.width };
                                return { topTrim: n, bottomTrim: a, leftTrim: i, rightTrim: o };
                            })(i),
                            d = r - l + 1,
                            c = s - o + 1,
                            p = document.createElement("canvas");
                        (p.width = d), (p.height = c);
                        p.getContext("2d", { willReadFrequently: true }).putImageData(n.getImageData(l, o, d, c), 0, 0), t(p);
                    }),
                    (a.onerror = (e) => n(e));
            });
        }
        async function u(e, t, n) {
            return new Promise((a) => {
                const i = new Image();
                (i.src = e.toDataURL()),
                    (i.onload = () => {
                        const e = document.createElement("canvas"),
                            o = i.width / i.height;
                        let s = t.finalCanvasDimensions.width,
                            l = t.finalCanvasDimensions.height;
                        s / o > l ? (s = l * o) : (l = s / o), (e.width = s), (e.height = l);
                        const r = e.getContext("2d"),
                            d = s * n,
                            c = l * n,
                            p = (s - d) / 2,
                            u = (l - c) / 2;
                        r.clearRect(0, 0, s, l), r.drawImage(i, p, u, d, c), a(e);
                    });
            });
        }
        function m(e, t) {
            return new Promise((n) => {
                const a = document.createElement("canvas");
                (a.width = t.finalCanvasDimensions.width), (a.height = t.finalCanvasDimensions.height);
                const i = a.getContext("2d");
                i.clearRect(0, 0, a.width, a.height);
                const { numberOfRows: o, designsPerRow: s } = t.patternSettings,
                    l = e.width,
                    r = e.height,
                    d = (t.finalCanvasDimensions.width - 10 * (s + 1)) / s,
                    c = r * (d / l),
                    p = a.height - 20 - c * o,
                    u = o > 1 ? p / (o - 1) : 0,
                    m = Array.from({ length: o }, (e, t) => 10 + t * (c + u));
                m.forEach((t, n) => {
                    const a = (n + 1) % 2 == 0 ? s + 1 : s;
                    for (let o = 0; o < a; o++) {
                        let a = 10 + o * (d + 10);
                        (n + 1) % 2 == 0 && (a -= d / 2);
                        const s = 0.75 * d,
                            p = 0.75 * c,
                            u = a + (d - s) / 2,
                            m = t + (c - p) / 2;
                        i.drawImage(e, 0, 0, l, r, u, m, s, p);
                    }
                }),
                    n(a);
            });
        }
        async function invertToBlack(e) {
            return new Promise((n) => {
                if (typeof e === "string") {
                    const t = new Image();
                    t.src = e;
                    t.onload = () => {
                        const a = document.createElement("canvas");
                        a.width = t.width;
                        a.height = t.height;
                        const i = a.getContext("2d", { willReadFrequently: true });
                        i.drawImage(t, 0, 0);
                        const o = i.getImageData(0, 0, a.width, a.height);
                        const s = o.data;
                        for (let e = 0; e < s.length; e += 4) {
                            if (s[e + 3] > 0) {
                                s[e] = 0;
                                s[e + 1] = 0;
                                s[e + 2] = 0;
                            }
                        }
                        i.putImageData(o, 0, 0);
                        n(a);
                    };
                    t.onerror = () => n(null);
                } else if (e instanceof HTMLCanvasElement) {
                    const t = e.getContext("2d", { willReadFrequently: true });
                    const a = t.getImageData(0, 0, e.width, e.height);
                    const i = a.data;
                    for (let e = 0; e < i.length; e += 4) {
                        if (i[e + 3] > 0) {
                            i[e] = 0;
                            i[e + 1] = 0;
                            i[e + 2] = 0;
                        }
                    }
                    t.putImageData(a, 0, 0);
                    n(e);
                } else {
                    n(null);
                }
            });
        }
        async function smartInvert(e) {
            return new Promise((n) => {
                const grayscaleThreshold = 100; // R, G, B must be within 100 of each other (maximum leniency to catch all near-grayscale)
                const lightnessThreshold = 0; // Convert grayscale pixels with lightness >= 0 to black (absolute maximum - converts all grayscale pixels)
                if (typeof e === "string") {
                    const t = new Image();
                    t.src = e;
                    t.onload = () => {
                        const a = document.createElement("canvas");
                        a.width = t.width;
                        a.height = t.height;
                        const i = a.getContext("2d", { willReadFrequently: true });
                        i.drawImage(t, 0, 0);
                        const o = i.getImageData(0, 0, a.width, a.height);
                        const s = o.data;
                        for (let e = 0; e < s.length; e += 4) {
                            if (s[e + 3] > 0) {
                                const r = s[e];
                                const d = s[e + 1];
                                const c = s[e + 2];
                                // Calculate max and min RGB values
                                const maxRGB = Math.max(r, d, c);
                                const minRGB = Math.min(r, d, c);
                                const maxDifference = maxRGB - minRGB;
                                // Check if pixel is grayscale or near-grayscale (R, G, B within threshold)
                                if (maxDifference <= grayscaleThreshold) {
                                    // Calculate lightness (average of R, G, B)
                                    const lightness = (r + d + c) / 3;
                                    // Convert to black if lightness exceeds threshold
                                    if (lightness >= lightnessThreshold) {
                                        s[e] = 0;
                                        s[e + 1] = 0;
                                        s[e + 2] = 0;
                                    }
                                }
                                // Non-grayscale colors are preserved (unchanged)
                            }
                        }
                        i.putImageData(o, 0, 0);
                        n(a);
                    };
                    t.onerror = () => n(null);
                } else if (e instanceof HTMLCanvasElement) {
                    const t = e.getContext("2d", { willReadFrequently: true });
                    const a = t.getImageData(0, 0, e.width, e.height);
                    const i = a.data;
                    for (let e = 0; e < i.length; e += 4) {
                        if (i[e + 3] > 0) {
                            const o = i[e];
                            const s = i[e + 1];
                            const l = i[e + 2];
                            // Calculate max and min RGB values
                            const maxRGB = Math.max(o, s, l);
                            const minRGB = Math.min(o, s, l);
                            const maxDifference = maxRGB - minRGB;
                            // Check if pixel is grayscale or near-grayscale (R, G, B within threshold)
                            if (maxDifference <= grayscaleThreshold) {
                                // Calculate lightness (average of R, G, B)
                                const lightness = (o + s + l) / 3;
                                // Convert to black if lightness exceeds threshold
                                if (lightness >= lightnessThreshold) {
                                    i[e] = 0;
                                    i[e + 1] = 0;
                                    i[e + 2] = 0;
                                }
                            }
                            // Non-grayscale colors are preserved (unchanged)
                        }
                    }
                    t.putImageData(a, 0, 0);
                    n(e);
                } else {
                    n(null);
                }
            });
        }
        async function blackBackground(e) {
            return new Promise((resolve) => {
                const PAD = 50;

                const process = (srcCanvas) => {
                    try {
                        const w = srcCanvas.width;
                        const h = srcCanvas.height;

                        // Build a pure black silhouette from non-transparent pixels
                        const silhouette = document.createElement("canvas");
                        silhouette.width = w;
                        silhouette.height = h;
                        const sCtx = silhouette.getContext("2d", { willReadFrequently: true });
                        sCtx.drawImage(srcCanvas, 0, 0);
                        const sData = sCtx.getImageData(0, 0, w, h);
                        const d = sData.data;
                        for (let i = 0; i < d.length; i += 4) {
                            const alpha = d[i + 3];
                            if (alpha > 0) {
                                d[i] = 0; d[i + 1] = 0; d[i + 2] = 0; d[i + 3] = 255; // solid black
                            } else {
                                d[i] = 0; d[i + 1] = 0; d[i + 2] = 0; d[i + 3] = 0;   // transparent
                            }
                        }
                        sCtx.putImageData(sData, 0, 0);

                        // Output canvas with 50px padding
                        const out = document.createElement("canvas");
                        out.width = w + PAD * 2;
                        out.height = h + PAD * 2;
                        const oCtx = out.getContext("2d", { willReadFrequently: true });
                        oCtx.clearRect(0, 0, out.width, out.height);

                        // Try to dilate the silhouette by ~50px using blur + threshold
                        const tmp = document.createElement("canvas");
                        tmp.width = out.width;
                        tmp.height = out.height;
                        const tCtx = tmp.getContext("2d", { willReadFrequently: true });
                        tCtx.clearRect(0, 0, tmp.width, tmp.height);

                        // Deterministic dilation by stamping offsets in a disk of radius PAD
                        const step = PAD >= 40 ? 3 : 2;
                        for (let dx = -PAD; dx <= PAD; dx += step) {
                            const maxDy = Math.floor(Math.sqrt(PAD * PAD - dx * dx));
                            for (let dy = -maxDy; dy <= maxDy; dy += step) {
                                oCtx.drawImage(silhouette, PAD + dx, PAD + dy);
                            }
                        }

                        // Draw the original design atop the black background
                        oCtx.drawImage(srcCanvas, PAD, PAD);
                        resolve(out);
                    } catch (err) {
                        resolve(null);
                    }
                };

                if (typeof e === "string") {
                    const img = new Image();
                    img.src = e;
                    img.onload = () => {
                        const base = document.createElement("canvas");
                        base.width = img.width;
                        base.height = img.height;
                        base.getContext("2d").drawImage(img, 0, 0);
                        process(base);
                    };
                    img.onerror = () => resolve(null);
                } else if (e instanceof HTMLCanvasElement) {
                    process(e);
                } else {
                    resolve(null);
                }
            });
        }

        async function f(e, t) {
            return new Promise((n) => {
                let a;
                if ("string" == typeof e) a = e;
                else {
                    if (!(e instanceof HTMLCanvasElement)) return void n(null);
                    a = e.toDataURL();
                }
                const i = new Image();
                (i.src = a),
                    (i.onload = () => {
                        const e = document.createElement("canvas");
                        (e.width = i.width), (e.height = i.height);
                        e.getContext("2d").drawImage(i, 0, 0),
                            e.toBlob(async function (e) {
                                const a = await e.arrayBuffer(),
                                    i = new Uint8Array(a),
                                    o = [137, 80, 78, 71, 13, 10, 26, 10];
                                for (let t = 0; t < o.length; t++) if (i[t] !== o[t]) return void n(e);
                                const s = Math.round(39.3701 * t),
                                    l = new Uint8Array([(s >>> 24) & 255, (s >>> 16) & 255, (s >>> 8) & 255, 255 & s, (s >>> 24) & 255, (s >>> 16) & 255, (s >>> 8) & 255, 255 & s, 1]),
                                    r = new Uint8Array([112, 72, 89, 115, ...l]),
                                    d = (function (e) {
                                        let t = -1;
                                        for (let n = 0; n < e.length; n++) t = (t >>> 8) ^ g[255 & (t ^ e[n])];
                                        return (-1 ^ t) >>> 0;
                                    })(r),
                                    c = new Uint8Array([0, 0, 0, 9, ...r, (d >>> 24) & 255, (d >>> 16) & 255, (d >>> 8) & 255, 255 & d]),
                                    p = new Uint8Array(i.length + c.length);
                                p.set(i.slice(0, 33), 0), p.set(c, 33), p.set(i.slice(33), 33 + c.length);
                                const u = new Blob([p], { type: "image/png" });
                                n(u);
                            }, "image/png");
                    }),
                    (i.onerror = (e) => n(null));
            });
        }
        !(function () {
            const t = localStorage.getItem("accessoriesResizePreferences");
            if (t) {
                e = JSON.parse(t);
                for (const t in e) {
                    const n = e[t];
                    if (n.tabs)
                        for (const e in n.tabs) {
                            const t = n.tabs[e];
                            if (t.leftImageData) t.leftImageData = null;
                            if (t.rightImageData) t.rightImageData = null;
                            if (t.leftImageUploaded) t.leftImageUploaded = false;
                            if (t.rightImageUploaded) t.rightImageUploaded = false;
                            if (!t.colorConversion) t.colorConversion = "original";
                        }
                }
            } else {
                n.forEach((t) => {
                    let n = t.tabs[0];
                    if ("TUMBLER" === t.name) n = "Two Sides";
                    e[t.name] = { selectedTab: n, tabs: {}, downloadaCopy: false };
                    t.tabs.forEach((n) => {
                        let a = "percent85";
                        if ("POP_SOCKET" === t.name) a = "percent100";
                        e[t.name].tabs[n] = {
                            scalingOption: a,
                            customScaleValue: "",
                            colorConversion: "original",
                            ...("2 Different Sides" === n && {
                                leftImageUploaded: false,
                                rightImageUploaded: false,
                                leftImageData: null,
                                rightImageData: null,
                            }),
                        };
                    });
                    if (void 0 !== t.fillArtwork) e[t.name].fillArtwork = t.fillArtwork;
                });
                a();
            }
        })(),
            document.addEventListener("click", function (e) {
                if (e.target.matches(".btn.btn-secondary.btn-edit") || e.target.matches(".btn-secondary.btn-edit")) {
                    const t = e.target,
                        o = Array.from(t.classList),
                        s = o
                            .find((e) => e.endsWith("-edit-btn"))
                            ?.replace("-edit-btn", "")
                            .toUpperCase();
                    if (o.includes("ZIP_HOODIE-edit-btn")) {
                        const cardEl = t.closest("div.mb-base");
                        if (cardEl) {
                            [
                                ".tote-resize-container",
                                ".throw-resize-container",
                                ".iphone-resize-container",
                                ".popsockets-resize-container",
                                ".mug-resize-container"
                            ].forEach(sel => {
                                const found = cardEl.querySelector(sel);
                                if (found) found.remove();
                            });
                        }
                        return;
                    }
                    if (!s) return;
                    const l = n.find((e) => e.name === s);
                    if (!l) return;
                    const r = t.closest("div.mb-base");
                    if (!r) return;
                    const d = r.querySelector(".form-row > .col-6:nth-child(2)");
                    if (!d) return;
                    i(l, d);
                }
            });
        const g = (() => {
            let e;
            const t = [];
            for (let n = 0; n < 256; n++) {
                e = n;
                for (let t = 0; t < 8; t++) {
                    if (1 & e) e = 3988292384 ^ (e >>> 1);
                    else e >>>= 1;
                }
                t[n] = e;
            }
            return t;
        })();
        async function y(e, t, n = false, a = 1, i = 2) {
            const o = document.querySelector(`.${t.resizeContainerClass} .resize-btn`);
            if (!n) {
                const e = document.querySelector(".delete-button");
                if (e && (e.click(), await h(300), t.additionalUploadSteps)) {
                    const e = document.querySelector(".btn.btn-outline-primary.btn-BACK.ng-star-inserted");
                    if (e) {
                        await h(300);
                        e.click();
                        await h(300);
                    } else if (o) {
                        o.title = "Back button not found. Cannot proceed with uploading.";
                    }
                }
            }
            const s = document.querySelector(t.uploadInputSelector);
            if (s) {
                const n = s.getAttribute("for"),
                    l = document.getElementById(n);
                if (l) {
                    const n = new DataTransfer(),
                        s = new File([e], "final_design.png", { type: "image/png" });
                    n.items.add(s), (l.files = n.files);
                    const r = new Event("change", { bubbles: true });
                    l.dispatchEvent(r), await h(300), await h(700);
                    const d = document.querySelector(".sci-icon.sci-error");
                    if (d && null !== d.offsetParent) {
                        if (a < i) {
                            await y(e, t, true, a + 1, i);
                        } else if (o) {
                            o.title = "Re-upload failed after multiple attempts.";
                        }
                    }
                } else if (o) {
                    o.title = "File input not found. Cannot proceed with uploading.";
                }
            } else if (o) {
                o.title = "Upload label not found. Cannot proceed with uploading.";
            }
        }
        function h(e) {
            return new Promise((t) => setTimeout(t, e));
        }
        function b(e, t) {
            C(e);
            const n = document.createElement("div");
            (n.className = "upload-areas-container"), (n.style.display = "flex"), (n.style.justifyContent = "space-between"), (n.style.gap = "16px"), (n.style.marginTop = "20px");
            const a = S("left", t),
                i = S("right", t);
            n.appendChild(a), n.appendChild(i);
            const o = e.querySelector(".scale-options-container");
            e.insertBefore(n, o.nextSibling);
        }
        function C(e) {
            const t = e.querySelector(".upload-areas-container");
            t && t.remove();
        }
        function S(t, n) {
            const i = document.createElement("div");
            (i.className = `upload-container ${t}-upload`),
                (i.style.border = "1.5px dashed #470CED"),
                (i.style.borderRadius = "8px"),
                (i.style.width = "calc(50% - 8px)"),
                (i.style.height = "120px"),
                (i.style.display = "flex"),
                (i.style.alignItems = "center"),
                (i.style.justifyContent = "center"),
                (i.style.boxSizing = "border-box"),
                (i.style.position = "relative"),
                (i.style.cursor = "pointer");
            const o = e[n.name].tabs["2 Different Sides"];
            if (o[`${t}ImageUploaded`]) {
                i.classList.add("success");
                i.style.borderColor = "#01BB87";
                i.innerHTML = "";
                const e = document.createElement("div");
                e.className = "upload-content";
                const s = document.createElement("img");
                (s.src = chrome.runtime.getURL("assets/loaded.svg")), (s.alt = "Loaded"), (s.style.width = "40px"), (s.style.height = "40px"), e.appendChild(s), i.appendChild(e);
                const l = document.createElement("button");
                (l.className = "delete-btn"),
                    (l.style.backgroundColor = "#fff5f5"),
                    (l.style.border = "none"),
                    (l.style.borderRadius = "50%"),
                    (l.style.width = "40px"),
                    (l.style.height = "40px"),
                    (l.style.display = "flex"),
                    (l.style.alignItems = "center"),
                    (l.style.justifyContent = "center"),
                    (l.style.cursor = "pointer"),
                    (l.style.position = "absolute"),
                    (l.style.right = "16px"),
                    (l.style.bottom = "16px");
                const d = document.createElement("img");
                (d.src = chrome.runtime.getURL("assets/clear.svg")),
                    (d.alt = "Clear"),
                    (d.style.width = "20px"),
                    (d.style.height = "20px"),
                    l.appendChild(d),
                    l.addEventListener("click", (e) => {
                        e.stopPropagation();
                        o[`${t}ImageData`] = null;
                        o[`${t}ImageUploaded`] = false;
                        a();
                        i.classList.remove("success");
                        i.style.borderColor = "#470CED";
                        i.innerHTML = "";
                        i.appendChild(S(t, n).firstChild);
                        r(n, i.closest(`.${n.resizeContainerClass}`));
                    }),
                    i.appendChild(l);
            } else {
                const e = document.createElement("div");
                (e.className = "upload-content"),
                    (e.style.display = "flex"),
                    (e.style.flexDirection = "column"),
                    (e.style.alignItems = "center");
                const t = document.createElement("img");
                (t.src = chrome.runtime.getURL("assets/draganddrop.svg")), (t.alt = "Drag and Drop"), (t.style.width = "40px"), (t.style.height = "40px"), (t.style.marginBottom = "8px");
                const n = document.createElement("p");
                (n.textContent = "Drag and drop"), (n.style.fontSize = "14px"), (n.style.fontWeight = "700"), (n.style.color = "#470CED"), (n.style.margin = "0");
                const a = document.createElement("p");
                (a.textContent = "PNG format"), (a.style.fontSize = "14px"), (a.style.color = "#9AA5B1"), (a.style.margin = "0"), e.appendChild(t), e.appendChild(n), e.appendChild(a), i.appendChild(e);
            }
            i.addEventListener("dragover", (e) => {
                e.preventDefault();
                i.style.borderColor = "#01BB87";
            });
            i.addEventListener("dragleave", () => {
                i.style.borderColor = "#470CED";
            });
            i.addEventListener("drop", (e) => {
                e.preventDefault();
                i.style.borderColor = "#470CED";
                w(e.dataTransfer.files[0], i, t, n);
            });
            i.addEventListener("click", (e) => {
                if (!e.target.closest(".delete-btn")) {
                    const e = document.createElement("input");
                    (e.type = "file"), (e.accept = "image/png");
                    e.onchange = () => {
                        w(e.files[0], i, t, n);
                    };
                    e.click();
                }
            });
            return i;
        }
        function w(t, n, i, o) {
            const s = e[o.name].tabs["2 Different Sides"];
            if (t && "image/png" === t.type) {
                const e = new FileReader();
                e.onload = () => {
                    const t = e.result;
                    s[`${i}ImageData`] = t;
                    s[`${i}ImageUploaded`] = true;
                    a();
                    n.classList.add("success");
                    n.style.borderColor = "#01BB87";
                    n.innerHTML = "";
                    const l = document.createElement("div");
                    l.className = "upload-content";
                    const d = document.createElement("img");
                    (d.src = chrome.runtime.getURL("assets/loaded.svg")), (d.alt = "Loaded"), (d.style.width = "40px"), (d.style.height = "40px"), l.appendChild(d), n.appendChild(l);
                    const c = document.createElement("button");
                    (c.className = "delete-btn"),
                        (c.style.backgroundColor = "#fff5f5"),
                        (c.style.border = "none"),
                        (c.style.borderRadius = "50%"),
                        (c.style.width = "40px"),
                        (c.style.height = "40px"),
                        (c.style.display = "flex"),
                        (c.style.alignItems = "center"),
                        (c.style.justifyContent = "center"),
                        (c.style.cursor = "pointer"),
                        (c.style.position = "absolute"),
                        (c.style.right = "16px"),
                        (c.style.bottom = "16px");
                    const p = document.createElement("img");
                    (p.src = chrome.runtime.getURL("assets/clear.svg")),
                        (p.alt = "Clear"),
                        (p.style.width = "20px"),
                        (p.style.height = "20px"),
                        c.appendChild(p),
                        c.addEventListener("click", (e) => {
                            e.stopPropagation();
                            s[`${i}ImageData`] = null;
                            s[`${i}ImageUploaded`] = false;
                            a();
                            n.classList.remove("success");
                            n.style.borderColor = "#470CED";
                            n.innerHTML = "";
                            n.appendChild(S(i, o).firstChild);
                            r(o, n.closest(`.${o.resizeContainerClass}`));
                        }),
                        n.appendChild(c),
                        r(o, n.closest(`.${o.resizeContainerClass}`));
                };
                e.readAsDataURL(t);
            } else {
                n.classList.add("error");
                n.style.borderColor = "#FF391F";
                n.innerHTML = "";
                const e = document.createElement("div");
                (e.className = "upload-content"),
                    (e.style.display = "flex"),
                    (e.style.alignItems = "center"),
                    (e.style.justifyContent = "center"),
                    (e.style.gap = "16px");
                const t = document.createElement("img");
                (t.src = chrome.runtime.getURL("assets/error.svg")), (t.alt = "Error"), (t.style.width = "40px"), (t.style.height = "40px");
                const a = document.createElement("div");
                a.className = "upload-text";
                const s = document.createElement("p");
                (s.textContent = "Invalid File"), (s.style.fontSize = "16px"), (s.style.color = "#FF391F"), (s.style.fontWeight = "bold"), (s.style.margin = "0");
                const l = document.createElement("p");
                (l.textContent = "Upload a PNG file."), (l.style.fontSize = "14px"), (l.style.color = "#9AA5B1"), (l.style.margin = "0"), a.appendChild(s), a.appendChild(l), e.appendChild(t), e.appendChild(a), n.appendChild(e);
                setTimeout(() => {
                    n.classList.remove("error");
                    n.style.borderColor = "#470CED";
                    n.innerHTML = "";
                    n.appendChild(S(i, o).firstChild);
                }, 2000);
            }
        }
        async function E(e, t, n) {
            const { trimDesign: a, resizeImage: i } = t.functionsEnabled;
            let o = e;
            if (a || i) {
                if (a) {
                    o = await p(e);
                }
                if (i) {
                    o = await u(o, t, n);
                }
            }
            return o;
        }
        !(function () {
            const container = document.querySelector(".product-editor");
            if (!container) return;
            const debounce = (func, delay) => {
                let timeout;
                return (...args) => {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        func(...args);
                    }, delay);
                };
            };
            const observerCallback = debounce(function () {
                container.querySelectorAll("div.mb-base").forEach((base) => {
                    n.forEach((product) => {
                        if (!base.querySelector(`.${product.resizeContainerClass}`) && e[product.name]) {
                            const editorCol = base.querySelector(".form-row > .col-6:nth-child(2)");
                            if (editorCol) i(product, editorCol);
                        }
                    });
                });
            }, 300);
            new MutationObserver(observerCallback).observe(container, { childList: true, subtree: true });
        })();
    })();
})();
