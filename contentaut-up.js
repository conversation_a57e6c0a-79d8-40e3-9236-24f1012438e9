!(function() {
    "use strict";
        const products = [
        {
            name: "STANDARD_TSHIRT",
            displayName: "Standard T-shirt",
            category: "Clothing Products",
            productLabel: "Shirts",
            cardId: "STANDARD_TSHIRT-card",
            editButtonClass: "STANDARD_TSHIRT-edit-btn",
            resizeContainerClass: "standard-resize-container",
            sidesOptions: ["Back", "Frontside (Pocket)", "Back & Pocket"],
            resizebtnClass: "resize-btn",
            colorClass: "color-groups-container",
            darkColors: "product-dark-colors-btn",
            lightColors: "product-light-colors-btn",
            allColors: "product-all-colors-btn",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "PREMIUM_TSHIRT",
            displayName: "Premium T-shirt",
            category: "Clothing Products",
            productLabel: "Shirts",
            cardId: "PREMIUM_TSHIRT-card",
            editButtonClass: "PREMIUM_TSHIRT-edit-btn",
            resizeContainerClass: "premium-resize-container",
            sidesOptions: ["Back", "Frontside (Pocket)", "Back & Pocket"],
            resizebtnClass: "resize-btn",
            colorClass: "color-groups-container",
            darkColors: "product-dark-colors-btn",
            lightColors: "product-light-colors-btn",
            allColors: "product-all-colors-btn",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "OVERSIZED_TSHIRT",
            displayName: "Comfort Colors Heavyweight T-shirt",
            category: "Clothing Products",
            productLabel: "Shirts",
            cardId: "OVERSIZED_TSHIRT-card",
            editButtonClass: "OVERSIZED_TSHIRT-edit-btn",
            resizeContainerClass: "oversized-resize-container",
            sidesOptions: ["Back", "Frontside (Pocket)", "Back & Pocket"],
            resizebtnClass: "resize-btn",
            colorClass: "color-groups-container",
            darkColors: "product-dark-colors-btn",
            lightColors: "product-light-colors-btn",
            allColors: "product-all-colors-btn",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "VNECK",
            displayName: "V-neck T-shirt",
            category: "Clothing Products",
            productLabel: "Shirts",
            cardId: "VNECK-card",
            editButtonClass: "VNECK-edit-btn",
            resizeContainerClass: "vneck-resize-container",
            sidesOptions: ["Back", "Frontside (Pocket)", "Back & Pocket"],
            resizebtnClass: "resize-btn",
            colorClass: "color-groups-container",
            darkColors: "product-dark-colors-btn",
            lightColors: "product-light-colors-btn",
            allColors: "product-all-colors-btn",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "TANK_TOP",
            displayName: "Tank Top",
            category: "Clothing Products",
            productLabel: "Shirts",
            cardId: "TANK_TOP-card",
            editButtonClass: "TANK_TOP-edit-btn",
            resizeContainerClass: "tank-resize-container",
            sidesOptions: ["Back", "Frontside (Pocket)", "Back & Pocket"],
            resizebtnClass: "resize-btn",
            colorClass: "color-groups-container",
            darkColors: "product-dark-colors-btn",
            lightColors: "product-light-colors-btn",
            allColors: "product-all-colors-btn",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "STANDARD_LONG_SLEEVE",
            displayName: "Long Sleeve Shirt",
            category: "Clothing Products",
            productLabel: "Shirts",
            cardId: "STANDARD_LONG_SLEEVE-card",
            editButtonClass: "STANDARD_LONG_SLEEVE-edit-btn",
            resizeContainerClass: "long-resize-container",
            sidesOptions: ["Back", "Frontside (Pocket)", "Back & Pocket"],
            resizebtnClass: "resize-btn",
            colorClass: "color-groups-container",
            darkColors: "product-dark-colors-btn",
            lightColors: "product-light-colors-btn",
            allColors: "product-all-colors-btn",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "RAGLAN",
            displayName: "Raglan",
            category: "Clothing Products",
            productLabel: "Shirts",
            cardId: "RAGLAN-card",
            editButtonClass: "RAGLAN-edit-btn",
            resizeContainerClass: "raglan-resize-container",
            sidesOptions: ["Back", "Frontside (Pocket)", "Back & Pocket"],
            resizebtnClass: "resize-btn",
            colorClass: "color-groups-container",
            darkColors: "product-dark-colors-btn",
            lightColors: "product-light-colors-btn",
            allColors: "product-all-colors-btn",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "STANDARD_SWEATSHIRT",
            displayName: "Standard Sweatshirt",
            category: "Clothing Products",
            productLabel: "Shirts",
            cardId: "STANDARD_SWEATSHIRT-card",
            editButtonClass: "STANDARD_SWEATSHIRT-edit-btn",
            resizeContainerClass: "sweatshirt-resize-container",
            sidesOptions: ["Back", "Frontside (Pocket)", "Back & Pocket"],
            resizebtnClass: "resize-btn",
            colorClass: "color-groups-container",
            darkColors: "product-dark-colors-btn",
            lightColors: "product-light-colors-btn",
            allColors: "product-all-colors-btn",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "STANDARD_PULLOVER_HOODIE",
            displayName: "Pullover Hoodie",
            category: "Clothing Products",
            productLabel: "Pullover Hoodie",
            cardId: "STANDARD_PULLOVER_HOODIE-card",
            editButtonClass: "STANDARD_PULLOVER_HOODIE-edit-btn",
            resizeContainerClass: "pullover-resize-container",
            sidesOptions: ["Back", "Frontside (Pocket)", "Back & Pocket"],
            resizebtnClass: "resize-btn",
            colorClass: "color-groups-container",
            darkColors: "product-dark-colors-btn",
            lightColors: "product-light-colors-btn",
            allColors: "product-all-colors-btn",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "ZIP_HOODIE",
            displayName: "Zip Hoodie",
            category: "Clothing Products",
            productLabel: "ZIP Hoodie",
            cardId: "ZIP_HOODIE-card",
            editButtonClass: "ZIP_HOODIE-edit-btn",
            resizeContainerClass: "zip-resize-container",
            sidesOptions: ["Back", "Frontside (Pocket)", "Back & Pocket"],
            resizebtnClass: "resize-btn",
            colorClass: "color-groups-container",
            darkColors: "product-dark-colors-btn",
            lightColors: "product-light-colors-btn",
            allColors: "product-all-colors-btn",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "POP_SOCKET",
            displayName: "PopSockets Grip",
            category: "Scalable Products",
            cardId: "POP_SOCKET-card",
            editButtonClass: "POP_SOCKET-edit-btn",
            resizeContainerClass: "popsockets-resize-container",
            scaleOptions: ["input#custom", "input#percent100", "input#percent85", "input#percent75", "input#pattern"],
            resizebtnClass: "resize-btn",
            colorClass: "pop-scalable-color",
            colorSwatch: "color-swatch",
            colorSketch: "btn btn-secondary icon",
            customHEX: "sketch-fields input",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "PHONE_CASE_APPLE_IPHONE",
            displayName: "iPhone Case",
            category: "Scalable Products",
            cardId: "PHONE_CASE_APPLE_IPHONE-card",
            editButtonClass: "PHONE_CASE_APPLE_IPHONE-edit-btn",
            resizeContainerClass: "iphone-resize-container",
            scaleOptions: ["input#custom", "input#percent100", "input#percent85", "input#percent75", "input#pattern"],
            resizebtnClass: "resize-btn",
            colorClass: "phone-scalable-color",
            colorSwatch: "color-swatch",
            colorSketch: "btn btn-secondary icon",
            customHEX: "sketch-fields input",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "TOTE_BAG",
            displayName: "Tote Bag",
            category: "Scalable Products",
            cardId: "TOTE_BAG-card",
            editButtonClass: "TOTE_BAG-edit-btn",
            resizeContainerClass: "tote-resize-container",
            scaleOptions: ["input#custom", "input#percent100", "input#percent85", "input#percent75", "input#pattern"],
            resizebtnClass: "resize-btn",
            colorClass: "tote-scalable-color",
            colorSwatch: "color-swatch",
            colorSketch: "btn btn-secondary icon",
            customHEX: "sketch-fields input",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "THROW_PILLOW",
            displayName: "Throw Pillow",
            category: "Scalable Products",
            cardId: "THROW_PILLOW-card",
            editButtonClass: "THROW_PILLOW-edit-btn",
            resizeContainerClass: "throw-resize-container",
            scaleOptions: ["input#custom", "input#percent100", "input#percent85", "input#percent75", "input#pattern"],
            resizebtnClass: "resize-btn",
            colorClass: "throw-scalable-color",
            colorSwatch: "color-swatch",
            colorSketch: "btn btn-secondary icon",
            customHEX: "sketch-fields input",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "TUMBLER",
            displayName: "Tumbler",
            category: "Tumbler Product",
            cardId: "TUMBLER-card",
            editButtonClass: "TUMBLER-edit-btn",
            resizeContainerClass: "tumbler-resize-container",
            sidesOptions: ["One Side", "Two Sides"],
            oneSidescaleOptions: ["input#custom", "input#percent100", "input#percent85", "input#percent75"],
            twoSidesscaleOptions: ["input#custom", "input#percent100", "input#percent85", "input#percent75", "input#pattern"],
            resizebtnClass: "resize-btn",
            colorClass: "color-groups-container",
            darkColors: "product-dark-colors-btn",
            lightColors: "product-light-colors-btn",
            allColors: "product-all-colors-btn",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        },
        {
            name: "MUGS",
            displayName: "Ceramic Mug",
            category: "Tumbler Product",
            cardId: "MUG-card",
            editButtonClass: "MUG-edit-btn",
            resizeContainerClass: "mug-resize-container",
            sidesOptions: ["One Side", "Two Sides"],
            oneSidescaleOptions: ["input#custom", "input#percent100", "input#percent85", "input#percent75"],
            twoSidesscaleOptions: ["input#custom", "input#percent100", "input#percent85", "input#percent75", "input#pattern"],
            resizebtnClass: "resize-btn",
            colorClass: "color-groups-container",
            darkColors: "product-dark-colors-btn",
            lightColors: "product-light-colors-btn",
            allColors: "product-all-colors-btn",
            priceClass: "nav-container",
            defaultPrices: "product-default-prices-btn",
            suggestedPrices: "product-suggested-prices-btn",
            averagePrices: "product-market-average-btn",
            maxPrices: "product-max-prices-btn",
        }
    ];

    const getProductByName = (name) => products.find(p => p.name === name);
    const getProductsByCategory = (category) => products.filter(p => p.category === category);

    const BASE_URL = "https://merch.amazon.com";
    const DESIGNS_PATH = "/designs";

    let isPopupActive = false;
    let messageRotationInterval;

    function closePopup(saveSettings = false) { 
        const overlay = document.querySelector('.snap-bulk-upload-overlay');
        if (saveSettings) {
            SaveSnapSettings(); 
        }
        overlay.classList.add('closing');
        overlay.classList.remove('show');

        setTimeout(() => {
            overlay.classList.remove('closing');
            overlay.style.display = 'none';

            document.body.style.overflow = '';
            document.body.style.height = '';
        }, 400);

        isPopupActive = false;
        if (messageRotationInterval) {
            clearInterval(messageRotationInterval);
        }
    }

    function isDesignsPage() {
        const currentUrl = window.location.href;
        return currentUrl.includes('merch.amazon.com') && currentUrl.includes('/designs');
    }

    function cleanupAutomation() {
        const button = document.querySelector('#snap-bulk-upload-btn');
        if (button) {
            button.remove();
        }
        const style = document.querySelector('#automation-style');
        if (style) {
            style.remove();
        }

        document.body.style.overflow = '';
        document.body.style.height = '';
    }

    function handleCheckboxState(checkbox, initialState = false) {
        if (checkbox) {
            checkbox.addEventListener('change', (e) => {

                if (e.target.checked) {
                    e.target.style.filter = 'none';
                } else {
                    e.target.style.filter = 'invert(97%) sepia(0%) saturate(0%) hue-rotate(246deg) brightness(103%) contrast(101%)';
                }

                updateStartAutomationButton();
            });

            checkbox.checked = initialState;
            checkbox.style.filter = initialState ? 'none' : 'invert(97%) sepia(0%) saturate(0%) hue-rotate(246deg) brightness(103%) contrast(101%)';
        }
    }

    function initAutomationCheckboxes() {
        const automationCheckboxes = {
            saveToDrafts: document.getElementById('save-to-drafts'),
            copyEnToAll: document.getElementById('copy-en-to-all'),
            autoTextSwap: document.getElementById('auto-text-swap')
        };

        Object.values(automationCheckboxes).forEach(checkbox => handleCheckboxState(checkbox));
    }

    function initTermsCheckbox() {
        const termsCheckbox = document.getElementById('terms-checkbox');
        handleCheckboxState(termsCheckbox);
    }

    let styleEl;

    function initAutomation() {
        if (document.querySelector('#snap-bulk-upload-btn')) return;

        styleEl = document.createElement("style");
        styleEl.id = 'automation-style';
        styleEl.textContent = `
            @font-face {
                font-family: "Amazon Ember";
                src: url("${chrome.runtime.getURL('fonts/Amazon-Ember-Medium.ttf')}") format("truetype");
                font-weight: 500;
                font-style: normal;
                font-display: swap;
            }

            @font-face {
                font-family: "Amazon Ember";
                src: url("${chrome.runtime.getURL('fonts/AmazonEmber_Bold.ttf')}") format("truetype");
                font-weight: 700;
                font-style: normal;
                font-display: swap;
            }

            * {
                font-weight: 500;
            }

            input::placeholder {
                color: #BFC7D2 !important;
                opacity: 1 !important;
            }
            
            input::-webkit-input-placeholder {
                color: #BFC7D2 !important;
                opacity: 1 !important;
            }
            
            input::-moz-placeholder {
                color: #BFC7D2 !important;
                opacity: 1 !important;
            }
            
            input:-ms-input-placeholder {
                color: #BFC7D2 !important;
                opacity: 1 !important;
            }
            
            input:-moz-placeholder {
                color: #BFC7D2 !important;
                opacity: 1 !important;
            }

            :root {
                --color-1: 0 100% 63%;
                --color-2: 270 100% 63%;
                --color-3: 210 100% 63%;
                --color-4: 195 100% 63%;
                --color-5: 90 100% 63%;
                --speed: 2s;
                --primary-color: #470CED;
                --text-secondary: #606D85;
                --border-color: #DCE0E5;
            }

            @keyframes rainbow-border {
                0% {
                    background-position: 0 0, 0 0, 0 0;
                }
                100% {
                    background-position: 0 0, 0 0, 200% 0;
                }
            }

            @keyframes rainbow-glow {
                0% {
                    background-position: 0 0;
                }
                100% {
                    background-position: 200% 0;
                }
            }

            #snap-bulk-upload-btn {
                position: fixed;
                bottom: 40px;
                right: 40px;
                z-index: 999;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                height: 44px;
                padding: 0 24px;
                font-size: 1rem;
                color: #fff;
                border: calc(0.08 * 1rem) solid transparent;
                border-radius: 12px;
                cursor: pointer;
                gap: 8px;
                text-decoration: none !important;
                box-shadow: none !important;
                outline: none !important;
                -webkit-tap-highlight-color: transparent;
                user-select: none;
                pointer-events: auto !important;
                background-image:
                    linear-gradient(#121213, #121213),
                    linear-gradient(#121213 50%, rgba(18,18,19,0.6) 80%, rgba(18,18,19,0)),
                    linear-gradient(
                        90deg,
                        hsl(var(--color-1)),
                        hsl(var(--color-5)),
                        hsl(var(--color-3)),
                        hsl(var(--color-4)),
                        hsl(var(--color-2))
                    );
                background-size: 100% 100%, 100% 100%, 200% 100%;
                background-position: 0 0, 0 0, 0 0;
                background-clip: padding-box, border-box, border-box;
                background-origin: border-box;
                animation: rainbow-border var(--speed) infinite linear;
                transition: all 0.5s ease-in-out;
            }

            #snap-bulk-upload-btn:hover {
                background-image:
                    linear-gradient(#2A00A0, #2A00A0),
                    linear-gradient(#2A00A0 50%, rgba(42,0,160,0.6) 80%, rgba(42,0,160,0)),
                    linear-gradient(
                        90deg,
                        hsl(var(--color-1)),
                        hsl(var(--color-5)),
                        hsl(var(--color-3)),
                        hsl(var(--color-4)),
                        hsl(var(--color-2))
                    );
            }

            #snap-bulk-upload-btn::before {
                content: "";
                position: absolute;
                z-index: -1;
                bottom: -20%;
                left: 50%;
                width: 60%;
                height: 20%;
                transform: translateX(-50%);
                background-image: linear-gradient(
                    90deg,
                    hsl(var(--color-1)),
                    hsl(var(--color-5)),
                    hsl(var(--color-3)),
                    hsl(var(--color-4)),
                    hsl(var(--color-2))
                );
                background-size: 200%;
                filter: blur(calc(0.8 * 1rem));
                pointer-events: none;
                animation: rainbow-glow var(--speed) infinite linear;
            }

            .snap-bulk-upload-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 99999;
                display: none;
                justify-content: center;
                align-items: center;
                opacity: 0;
                transition: opacity 0.3s ease-out;
                overflow: hidden;
            }


            .snap-bulk-upload-overlay.show {
                display: flex;
                opacity: 1;
            }

            .snap-bulk-upload-container {
                width: 994px;
                max-height: 96vh;
                min-height: 500px;
                background: #F7F8FA;
                border-radius: 28px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, calc(-50% + 30px)) scale(0.95);
                z-index: 100000;
                padding: 24px;
                display: flex;
                flex-direction: column;
                gap: 16px;
                opacity: 0;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                overflow-y: auto;
                scrollbar-width: thin;
                scrollbar-color: #DCE0E5 transparent;
                scroll-behavior: smooth;
                overscroll-behavior: contain;
            }

            .snap-bulk-upload-container::-webkit-scrollbar {
                width: 8px;
            }

            .snap-bulk-upload-container::-webkit-scrollbar-track {
                background: transparent;
                margin: 10px 0;
            }

            .snap-bulk-upload-container::-webkit-scrollbar-thumb {
                background-color: #DCE0E5;
                border-radius: 20px;
                border: 2px solid #F7F8FA;
            }

            .snap-bulk-upload-container::-webkit-scrollbar-thumb:hover {
                background-color: #C7CDD5;
            }

            @media screen and (max-height: 700px) {
                .snap-bulk-upload-container {
                    max-height: 96vh;
                    top: 48%;
                }
                
                .snap-bulk-upload-overlay.show .snap-bulk-upload-container {
                    max-height: 96vh;
                }
            }

            .snap-bulk-upload-overlay.show .snap-bulk-upload-container {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
                max-height: 96vh;
            }

            .snap-bulk-upload-overlay.closing .snap-bulk-upload-container {
                transform: translate(-50%, calc(-50% - 30px)) scale(0.95);
                opacity: 0;
            }

            .upload-tray-container {
                width: 100%;
                background: #FFFFFF;
                border-radius: 10px;
                display: flex;
                flex-direction: column;
                padding: 24px;
                gap: 16px;
                margin: 0;
            }

            .upload-tray-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 10px;
                margin: 0;
            }

            .upload-tray-header .header-left {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .upload-tray-header .header-right {
                display: flex;
                align-items: center;
            }

            .donation-btn {
                height: 40px;
                width: auto;
                cursor: pointer;
                display: block;
                position: relative;
            }

            .upload-tray-header h2 {
                font-family: "Amazon Ember";
                font-weight: 700;
                font-size: 16px;
                color: #000000;
                margin: 0;
            }

            .drag-drop-area, .loaded-files {
                width: 100%;
                box-sizing: border-box;
                border-radius: 6px;
                margin: 0;
            }

            .dashed-border {
                border-style: none;
                background: linear-gradient(90deg, #DCE0E5 4px, transparent 0) repeat-x,
                           linear-gradient(90deg, #DCE0E5 4px, transparent 0) repeat-x,
                           linear-gradient(0deg, #DCE0E5 4px, transparent 0) repeat-y,
                           linear-gradient(0deg, #DCE0E5 4px, transparent 0) repeat-y;
                background-size: 10px 1.5px, 10px 1.5px, 1.5px 10px, 1.5px 10px;
                background-position: 0px 0px, 0px 100%, 0px 0px, 100% 0px;
            }

            .drag-drop-area {
                height: 122px;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                gap: 20px;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.2s ease-in-out;
            }

            .drag-drop-area.drag-active {
                background-color: #470CED;
                border: none;
                background-image: none;
            }

            .drag-drop-area.drag-active img {
                filter: brightness(0) invert(1);
            }

            .drag-drop-area.drag-active .primary-text,
            .drag-drop-area.drag-active .secondary-text {
                color: #FFFFFF;
            }

            .drag-drop-area::before {
                display: none;
            }

            .drag-drop-area img {
                width: 60px;
                height: 47.72px;
                flex-shrink: 0;
                position: relative;
                top: -2px;
            }

            .drag-drop-area .text-container {
                display: flex;
                flex-direction: column;
                gap: 4px;
                align-items: flex-start;
                white-space: nowrap;
            }

            .drag-drop-area p {
                font-family: "Amazon Ember";
                font-weight: 500;
                margin: 0;
                white-space: nowrap;
                text-align: left;
                width: 100%;
            }

            .drag-drop-area .primary-text {
                font-family: "Amazon Ember";
                font-size: 14px;
                line-height: 17px;
                font-weight: 700;
                color: #606F95;
                text-align: left;
            }

            .drag-drop-area .secondary-text {
                font-family: "Amazon Ember";
                font-size: 12px;
                line-height: 14px;
                font-weight: 500;
                color: #606F95;
                text-align: left;
            }

            .lottie-icon {
                width: 24px !important;
                height: 24px !important;
                min-width: 24px !important;
                min-height: 24px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                flex-shrink: 0 !important;
                margin: 0 !important;
                padding: 0 !important;
                background: transparent !important;
                border: none !important;
                box-shadow: none !important;
                outline: none !important;
                overflow: visible !important;
            }

            .lottie-icon svg {
                width: 100% !important;
                height: 100% !important;
                display: block !important;
                fill: currentColor !important;
                stroke: currentColor !important;
            }

            .button-text {
                font-family: "Amazon Ember";
                font-weight: 500;
                text-decoration: none !important;
                white-space: nowrap;
            }

            .loaded-files {
                width: 100%;
                height: 72px;
                padding: 20px 24px;
                box-sizing: border-box;
                display: none;
                border-style: none;
                background: linear-gradient(90deg, #01BB87 4px, transparent 0) repeat-x,
                           linear-gradient(90deg, #01BB87 4px, transparent 0) repeat-x,
                           linear-gradient(0deg, #01BB87 4px, transparent 0) repeat-y,
                           linear-gradient(0deg, #01BB87 4px, transparent 0) repeat-y;
                background-size: 10px 1.5px, 10px 1.5px, 1.5px 10px, 1.5px 10px;
                background-position: 0px 0px, 0px 100%, 0px 0px, 100% 0px;
            }

            .loaded-files.active {
                display: flex;
                gap: 24px;
            }

            .files-main {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .files-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .files-label {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .files-label span {
                font-family: "Amazon Ember";
                font-weight: 500;
                color: var(--text-secondary);
                line-height: 10px;
            }

            .files-counter {
                height: 17px;
                padding: 0.5px 8.5px;
                background: rgba(1, 187, 135, 0.1);
                border-radius: 2px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .files-counter span {
                font-family: "Amazon Ember";
                font-weight: 500;
                color: #01BB87;
                line-height: 16px;
            }

            .progress-info {
                font-family: "Amazon Ember";
                font-weight: 500;
                color: var(--text-secondary);
                line-height: 8px;
            }

            .snap-progress-bar {
                width: 100%;
                height: 5px;
                background: #F7F8FA;
                border-radius: 10px;
                overflow: hidden;
            }

            .snap-progress-fill {
                height: 100%;
                background: #01BB87;
                border-radius: 10px;
                transition: width 1s ease-in-out;
                width: 0;
                max-width: 100%;
            }

            .snap-progress-fill.active {
                width: 0;
                max-width: 100%;
            }

            .clear-section {
                display: flex;
                align-items: center;
            }

            .clear-all-btn {
                width: 32px;
                height: 32px;
                background: rgba(250, 88, 57, 0.05);
                border-radius: 40px;
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background 0.2s ease-in-out;
                position: relative;
            }

            .clear-all-btn[data-tooltip]:before {
                content: attr(data-tooltip);
                position: absolute;
                bottom: calc(100% + 8px);
                left: 50%;
                transform: translateX(-50%);
                padding: 4px 12px;
                background-color: #1F2937;
                color: white;
                font-size: 12px;
                border-radius: 999px;
                white-space: nowrap;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                z-index: 1000;
                pointer-events: none;
            }

            .clear-all-btn[data-tooltip]:after {
                content: '';
                position: absolute;
                bottom: calc(100% + 4px);
                left: 50%;
                transform: translateX(-50%);
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #1F2937;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                z-index: 1000;
                pointer-events: none;
            }

            .clear-all-btn[data-tooltip]:hover:before,
            .clear-all-btn[data-tooltip]:hover:after {
                opacity: 1;
                visibility: visible;
            }

            .clear-all-btn:hover {
                background: rgba(250, 88, 57, 0.1);
            }

            .clear-all-btn img {
                width: 14.64px;
                height: 15.99px;
            }

            .products-options-container {
                width: 100%;
                background: #FFFFFF;
                border-radius: 10px;
                display: flex;
                flex-direction: column;
                padding: 24px;
                gap: 16px;
                margin: 0;
            }

            .products-options-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 10px;
                margin: 0;
            }

            .header-left {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .header-left img {
                width: 20.76px;
                height: 20.76px;
            }

            .header-left h2 {
                font-family: "Amazon Ember";
                font-weight: 700;
                font-size: 16px;
                color: #000000;
                margin: 0;
            }

            .tip-container {
                display: flex;
                align-items: center;
                position: relative;
            }

            .tip-icon {
                width: 24px;
                height: 24px;
                background: #470CED;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 2;
            }

            .tip-icon img {
                width: 14px;
                height: 14px;
                filter: brightness(0) invert(1);
            }

            .automation-tip {
                display: flex;
                align-items: center;
                background: rgba(0, 122, 255, 0.1);
                border-radius: 4px;
                padding: 4px 10px 4px 10px;
                margin-left: 10px;
            }

            .automation-tip span {
                font-family: "Amazon Ember";
                font-size: 12px;
                color: #470CED !important;
                line-height: 16px;
            }

            .clothing-products-card {
                width: 100%;
                border: 1.5px solid #fafafa;
                border-radius: 8px;
                padding: 24px;
                background: linear-gradient(135deg, rgba(250, 250, 250, 1) 0%, rgba(250, 250, 250, 0) 100%);
            }

            .clothing-products-card.off {
                border: none;
                background: linear-gradient(137.14deg, rgba(250, 250, 250, 1) 0%, rgba(250, 250, 250, 0) 100%);
            }

            .clothing-products-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 0px;
            }

            .clothing-products-card.off .clothing-products-header {
                margin-bottom: 0;
            }

            .clothing-products-card.off .clothing-products-controls {
                display: none;
            }

            .toggle-container {
                display: flex;
                align-items: center;
            }

            .toggle-btn {
                width: 36px;
                height: 18px;
                background-color: #470CED;
                border-radius: 18px;
                padding: 2px;
                border: none;
                cursor: pointer;
                position: relative;
                transition: background-color 0.3s;
                display: flex;
                align-items: center;
            }

            .toggle-btn:not(.active) {
                background-color: #cfd4d4;
            }

            .toggle-handle {
                width: 12px;
                height: 12px;
                background-color: white;
                border-radius: 50%;
                position: absolute;
                top: 50%;
                left: 3px;
                transform: translateY(-50%);
                transition: transform 0.3s;
            }

            .toggle-btn.active .toggle-handle {
                transform: translate(18px, -50%);
            }

            .header-text {
                font-family: "Amazon Ember";
                font-weight: 500;
                font-size: 14px;
                color: #000000;
                display: flex;
                align-items: center;
                margin: 0;
            }

            .clothing-products-controls {
                display: flex;
                flex-direction: row;
                gap: 10px;
                margin-top: 20px;
            }

            .clothing-products-controls.off {
                opacity: 0.5;
                pointer-events: none;
            }

            .control-group {
                display: flex;
                flex-direction: column;
                gap: 0px;
                width: calc((100% - 40px) / 5);
            }

            .scalable-products-controls {
                display: flex;
                flex-direction: row;
                gap: 10px;
                margin: 20px 0 0 0;
                width: 100%;
                padding-right: 0px;
            }

            .scalable-products-card {
                width: 100%;
                border: 1.5px solid #fafafa;
                border-radius: 8px;
                padding: 24px 24px 24px 24px;
                margin: 0;
                background: linear-gradient(135deg, rgba(250, 250, 250, 1) 0%, rgba(250, 250, 250, 0) 100%);
            }

            .scalable-products-card.off {
                border: none;
                background: linear-gradient(137.14deg, rgba(250, 250, 250, 1) 0%, rgba(250, 250, 250, 0) 100%);
            }

            .scalable-products-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 0px;
            }

            .scalable-products-card.off .scalable-products-header {
                margin-bottom: 0;
            }

            .scalable-products-card.off .scalable-products-controls {
                display: none;
            }

            .scalable-products-card .control-group {
                display: flex;
                flex-direction: column;
                gap: 0;
                width: calc((100% - 40px) / 5);
                margin: 0;
            }

            .control-group-wrapper {
                width: 100%;
                display: flex;
                gap: 8px;
            }
            
            .custom-scale-input {
                display: flex;
                align-items: center;
                border: 1.5px solid #DCE0E5;
                border-radius: 4px;
                padding: 0;
                height: 40px;
                width: 100%;
                background: #FFFFFF;
            }
            
            .percent-prefix {
                width: 32px !important;
                min-width: 32px !important;
                max-width: 32px !important;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #DCE0E5;
                border-radius: 4px 0 0 4px;
                flex-shrink: 0 !important;
                box-sizing: border-box !important;
            }
            
            .percent-prefix * {
                all: unset !important;
            }
            
            .percent-prefix span {
                font-family: "Amazon Ember" !important;
                font-size: 14px !important;
                color: #606D85 !important;
                display: block !important;
                width: 100% !important;
                text-align: center !important;
            }

            .scale-input {
                flex: 1;
                border: none;
                outline: none;
                font-family: "Amazon Ember";
                font-size: 12px;
                color: #181818;
                background: transparent;
                padding: 0 10px;
                height: 100%;
            }
            
            .scale-input::placeholder {
                color: #BFC7D2 !important;
            }

            .product-type-label {
                display: inline-flex;
                padding: 0 8px;
                height: 17px;
                background: rgba(0, 122, 255, 0.1);
                border: 1px solid #470CED;
                border-radius: 2px;
                align-items: center;
                margin-bottom: 4px;
                width: fit-content;
            }

            .product-type-label span {
                font-family: "Amazon Ember";
                font-size: 10px;
                font-weight: 500;
                color: #470CED;
                white-space: nowrap;
            }

            .control-label {
                font-family: "Amazon Ember";
                font-weight: 500;
                font-size: 14px;
                color: #000000;
                margin-bottom: 4px;
                width: fit-content;
            }

            .snap-dropdown {
                position: relative;
                width: 100%;
                cursor: pointer;
                user-select: none;
            }

            .snap-dropdown .dropdown-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 12px;
                height: 40px;
                border: 1.5px solid #DCE0E5;
                border-radius: 4px;
                background: white;
                box-sizing: border-box;
                transition: border-color 0.2s ease;
                outline: none;
            }

            .snap-dropdown.focused .dropdown-header {
                border-color: #470CED;
                outline: none;
                box-shadow: none;
            }

            .snap-dropdown .dropdown-header span {
                font-family: "Amazon Ember";
                font-weight: 500;
                font-size: 12px;
                color: #181818;
                line-height: 40px;
            }

            .snap-dropdown .dropdown-header img {
                width: 16px;
                height: 16px;
            }

            .snap-dropdown .dropdown-menu {
                max-height: 300px;
                overflow-y: auto;
            }

            .snap-dropdown .dropdown-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 12px;
                font-family: "Amazon Ember";
                font-weight: 500;
                font-size: 12px;
                color: #181818;
                transition: background-color 0.2s ease;
            }

            .snap-dropdown .dropdown-item:hover {
                background: #F3F4F6;
            }

            .snap-dropdown .dropdown-item.selected {
                font-weight: 700;
                color: #470CED;
            }

            .snap-bulk-upload-overlay * {
                -webkit-tap-highlight-color: transparent !important;
                -webkit-touch-callout: none !important;
                -webkit-user-select: none !important;
                -khtml-user-select: none !important;
                -moz-user-select: none !important;
                -ms-user-select: none !important;
                user-select: none !important;
                outline: none !important;
            }

            .snap-bulk-upload-overlay button:focus {
                outline: none !important;
            }

            .snap-bulk-upload-overlay input[type="file"] {
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                outline: none;
                border: none;
                background: transparent;
            }

            .snap-bulk-upload-overlay input[type="file"]:focus {
                outline: none;
                box-shadow: none;
            }

            .custom-scale-input:focus-within,
                border-color: #470CED !important;
                outline: none;
                box-shadow: 0 0 0 2px rgba(71, 12, 237, 0.1);
            }

            .custom-scale-input.disabled,
                opacity: 0.5;
                pointer-events: none;
            }

            .custom-scale-control-group .control-label,
            .control-group-custom-colors-control-group .control-label,
            .tumbler-custom-scale-control-group .control-label {
                display: none;
            }

            .custom-scale-control-group,
            .control-group-custom-colors-control-group {
                align-self: flex-end;
                margin-top: auto;
            }

            .scale-options-control-group + .custom-scale-control-group,
            .color-options-control-group + .control-group-custom-colors-control-group {
                margin-top: 8px;
            }

            .custom-scale-input:focus-within,
            .scale-input:focus,
                outline: none !important;
                box-shadow: none !important;
                border-color: #DCE0E5 !important;
            }

            input:focus,
            textarea:focus,
            select:focus {
                outline: none !important;
                box-shadow: none !important;
            }

            .custom-scale-input input:focus {
                outline: none !important;
                box-shadow: none !important;
            }

            .snap-dropdown.focused .dropdown-header {
                border-color: #470CED !important;
            }

            .custom-scale-input:focus-within {
                border-color: #470CED !important;
                outline: none !important;
                box-shadow: none !important;
            }

            .scale-input:focus {
                outline: none !important;
                box-shadow: none !important;
            }

            .percent-prefix:focus {
                outline: none !important;
                box-shadow: none !important;
            }

            .snap-dropdown.focused .dropdown-header {
                border-color: #470CED !important;
                outline: none !important;
                box-shadow: none !important;
            }

            .custom-scale-input:focus-within {
                border-color: #470CED !important;
                outline: none !important;
                box-shadow: none !important;
            }

            .custom-scale-input:focus-within .percent-prefix {
                background: #470CED !important;
            }

            .custom-scale-input:focus-within .percent-prefix span {
                color: #FFFFFF !important;
            }

            .custom-scale-input.error {
                border-color: #FF391F !important;
            }

            .custom-scale-input.error .percent-prefix {
                background: #FF391F !important;
            }

            .custom-scale-input.error .percent-prefix span {
                color: #FFFFFF !important;
            }

            .scale-input {
                flex: 1;
                border: none;
                outline: none;
                font-family: "Amazon Ember";
                font-size: 12px;
                color: #181818;
                background: transparent;
                padding: 0 10px;
                height: 100%;
            }

            .scale-input.error {
                color: #FF391F !important;
            }

            .color-options-control-group .dropdown-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 12px;
                font-family: "Amazon Ember";
                font-weight: 400;
                font-size: 12px;
                color: #181818;
                transition: background-color 0.2s ease;
            }

            .color-options-control-group .color-indicator {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                border: 0.5px solid #E5E5E5;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                filter: none;
            }

            .color-options-control-group .dropdown-header {
                display: flex;
                align-items: center;
                gap: 10px !important;
                padding: 0 12px;
            }

            .color-options-control-group .dropdown-header .color-indicator {
                width: 10px !important;
                height: 10px !important;
                min-width: 10px !important;
                border-radius: 50%;
                border: 0.5px solid #E5E5E5;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .color-options-control-group .dropdown-header .color-indicator img {
                width: 10px !important;
                height: 10px !important;
                display: block;
            }

            .color-options-control-group .dropdown-header {
                display: flex;
                align-items: center;
                gap: 10px !important;
                padding: 0 12px;
                justify-content: flex-start !important;
            }

            .color-options-control-group .dropdown-header span {
                text-align: left !important;
                justify-content: flex-start !important;
            }

            .color-options-control-group .dropdown-header .color-indicator {
                width: 10px !important;
                height: 10px !important;
                min-width: 10px !important;
                border-radius: 50%;
                border: 0.5px solid #E5E5E5;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .color-options-control-group .dropdown-header .color-indicator img {
                width: 10px !important;
                height: 10px !important;
                display: block;
            }

            .color-options-control-group .dropdown-header {
                display: flex;
                align-items: center;
                padding: 0 12px;
                justify-content: space-between !important;
            }

            .color-options-control-group .dropdown-header .header-content {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .color-options-control-group .dropdown-header .color-indicator {
                width: 10px !important;
                height: 10px !important;
                min-width: 10px !important;
                border-radius: 50%;
                border: 0.5px solid #E5E5E5;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .color-options-control-group .dropdown-header span {
                text-align: left !important;
            }

            .color-options-control-group .dropdown-header img {
                margin-left: auto;
                padding-right: 0;
            }

            .control-group.custom-scale-control-group,
            .control-group.tumbler-custom-scale-control-group {
                width: 100% !important;
            }

            .control-group.custom-scale-control-group .custom-scale-input,
            .control-group.tumbler-custom-scale-control-group .custom-scale-input {
                width: 100% !important;
            }

            .control-group {
                display: flex;
                flex-direction: column;
                gap: 0px;
                width: calc((100% - 40px) / 5);
            }

            .control-group.custom-scale-control-group,
            .control-group.colors-control-group,
            .control-group.tumbler-custom-scale-control-group {
                width: calc((100% - 40px) / 5) !important;
            }

            .tumbler-products-controls .control-group.tumbler-custom-scale-control-group {
                width: calc((100% - 40px) / 5) !important;
            }

            .tumbler-products-controls .control-group.tumbler-custom-scale-control-group .custom-scale-input {
                width: 100% !important;
            }

            .tumbler-products-card {
                width: 100%;
                border: 1.5px solid #fafafa;
                border-radius: 8px;
                padding: 24px;
                background: linear-gradient(135deg, rgba(250, 250, 250, 1) 0%, rgba(250, 250, 250, 0) 100%);
                margin-top: 0px;
            }

            .tumbler-products-card.off {
                border: none;
                background: linear-gradient(137.14deg, rgba(250, 250, 250, 1) 0%, rgba(250, 250, 250, 0) 100%);
            }

            .tumbler-products-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 0px;
            }

            .tumbler-products-controls {
                display: flex;
                flex-direction: row;
                gap: 10px;
                margin-top: 20px;
            }

            .tumbler-products-controls.off {
                display: none;
            }

            .tumbler-products-card .tumbler-custom-scale-control-group .control-label {
                display: none;
            }

            .tumbler-products-card .tumbler-custom-scale-control-group {
                margin-top: auto;
            }

            .tumbler-products-card .tumbler-custom-scale-control-group .custom-scale-input {
                margin-bottom: 0;
            }

            .tumbler-products-card .tumbler-custom-scale-control-group .custom-scale-input:focus-within {
                border-color: #470CED;
                outline: none;
                box-shadow: none;
            }

            .tumbler-products-card .tumbler-custom-scale-control-group .scale-input:focus {
                outline: none;
                box-shadow: none;
                border-color: #DCE0E5;
            }

            .mug-products-card {
                width: 100%;
                border: 1.5px solid #fafafa;
                border-radius: 8px;
                padding: 24px;
                background: linear-gradient(135deg, rgba(250, 250, 250, 1) 0%, rgba(250, 250, 250, 0) 100%);
                margin-top: 0px;
            }

            .mug-products-card.off {
                border: none;
                background: linear-gradient(137.14deg, rgba(250, 250, 250, 1) 0%, rgba(250, 250, 250, 0) 100%);
            }

            .mug-products-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 0px;
            }

            .mug-products-controls {
                display: flex;
                flex-direction: row;
                gap: 10px;
                margin-top: 20px;
            }

            .mug-products-controls.off {
                display: none;
            }

            .mug-products-card .mug-custom-scale-control-group .control-label {
                display: none;
            }

            .mug-products-card .mug-custom-scale-control-group {
                margin-top: auto;
            }

            .mug-products-card .mug-custom-scale-control-group .custom-scale-input {
                margin-bottom: 0;
            }

            .mug-products-card .mug-custom-scale-control-group .custom-scale-input:focus-within {
                border-color: #470CED;
                outline: none;
                box-shadow: none;
            }

            .mug-products-card .mug-custom-scale-control-group .scale-input:focus {
                outline: none;
                box-shadow: none;
                border-color: #DCE0E5;
            }

            .terms-container {
                width: 100%;
                background: #FFFFFF;
                border-radius: 10px;
                display: flex;
                flex-direction: column;
                padding: 24px;
                gap: 16px;
                margin-top: 0px;
            }

            .terms-content {
                display: flex;
                align-items: center;
                gap: 10px !important;
            }

            .terms-checkbox {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .terms-checkbox-input {
                width: 20px;
                height: 20px;
                appearance: none;
                background-color: transparent;
                border: none;
                cursor: pointer;
                transition: background-color 0.2s ease;
            }

            .terms-checkbox-input:checked {
                background-color: transparent;
                background-image: url("${chrome.runtime.getURL('assets/checkbox-ic.svg')}");
                background-size: 20px 20px;
                background-position: center;
                background-repeat: no-repeat;
            }

            .terms-checkbox-input:not(:checked) {
                background-image: url("${chrome.runtime.getURL('assets/checkbox-ic.svg')}");
                background-size: 20px 20px;
                background-position: center;
                background-repeat: no-repeat;
                filter: invert(97%) sepia(0%) saturate(0%) hue-rotate(246deg) brightness(103%) contrast(101%);
            }

            .terms-checkbox-label {
                font-family: "Amazon Ember";
                font-weight: 400;
                font-size: 13px;
                color: #000000;
                cursor: pointer;
                user-select: none;
                margin-bottom: 0;
                display: block;
            }

            .automate-container {
                width: 100%;
                background-color: #FFFFFF;
                border-radius: 10px;
                padding: 24px;
                margin-top: 0px;
            }

            .automate-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
            }

            .automate-options {
                display: flex;
                gap: 20px;
            }

            .automate-option {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .automate-option.text-swap {
                gap: 10px;
            }

            .text-swap-link {
                color: #470CED !important;
                text-decoration: underline !important;
                cursor: pointer;
                display: flex;
                align-items: center;
            }

            .text-swap-link:hover {
                color: #470CED !important;
                text-decoration: underline !important;
            }

            .text-swap-link .text-link {
                padding-left: 8px;
            }

            .text-swap-link .text-link:hover {
                color: #470CED !important;
                text-decoration: underline !important;
            }

            .text-swap-link .gap-spacer {
                width: 4px;
            }

            .text-swap-link .pdf-icon {
                width: 32px;
                height: 14px;
            }

            .automate-checkbox {
                width: 20px;
                height: 20px;
                appearance: none;
                background-color: transparent;
                border: none;
                cursor: pointer;
                transition: background-color 0.2s ease;
            }

            .automate-checkbox:checked {
                background-color: transparent;
                background-image: url("${chrome.runtime.getURL('assets/checkbox-ic.svg')}");
                background-size: 20px 20px;
                background-position: center;
                background-repeat: no-repeat;
            }

            .automate-checkbox:not(:checked) {
                background-image: url("${chrome.runtime.getURL('assets/checkbox-ic.svg')}");
                background-size: 20px 20px;
                background-position: center;
                background-repeat: no-repeat;
                filter: invert(97%) sepia(0%) saturate(0%) hue-rotate(246deg) brightness(103%) contrast(101%);
            }

            .automate-label {
                font-family: "Amazon Ember";
                font-weight: 500;
                font-size: 13px;
                color: #000000;
                cursor: pointer;
                user-select: none;
                margin-bottom: 0;
                display: block;
            }

            .automate-label .text-link {
                color: #470CED !important;
                text-decoration: underline;
                padding: 0 4px;
                display: inline-block;
            }

            .text-container {
                display: flex;
                align-items: center;
                gap: 0;
            }

            .pdf-icon {
                width: 32px;
                height: 14px;
                margin-left: 0;
            }

            .automate-buttons {
                display: flex;
                gap: 10px;
            }

            .automate-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 32px;
                padding: 0 16px;
                font-family: "Amazon Ember";
                font-weight: 500;
                font-size: 12px;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .close-btn {
                border: 1.5px solid #470CED;
                color: #470CED;
                background-color: transparent;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                padding: 0 16px;
                gap: 0;
            }

            .close-btn:hover {
                background-color: #470CED;
                color: #FFFFFF;
                border-color: #470CED;
            }

            .close-btn:hover .save-icon {
                filter: brightness(0) invert(1);
            }

            .save-icon {
                width: 16px;
                height: 16px;
                margin-right: 4px;
                transition: filter 0.2s ease;
            }

            .close-btn.loading {
                background-color: #470CED !important;
                color: #FFFFFF !important;
                border-color: #470CED !important;
                pointer-events: none;
                justify-content: center;
                gap: 0;
            }

            .close-btn.loading:hover {
                background-color: #470CED !important;
                color: #FFFFFF !important;
            }
            
            .close-btn.loading .save-icon,
            .close-btn.loading > :not(.save-spinner) {
                display: none !important;
            }

            .save-spinner {
                display: none;
                width: 16px;
                height: 16px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-top-color: #FFFFFF;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            .close-btn.loading .save-spinner {
                display: block !important;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .save-settings-btn {
                border: 1px solid #C7CDD5;
                color: #470CED;
                background-color: transparent;
                transition: all 0.2s ease;
            }

            .save-settings-btn:hover {
                background-color: #470CED;
                color: #FFFFFF;
                border-color: #470CED;
            }

            .save-settings-btn:hover .save-icon {
                filter: brightness(0) invert(1);
            }

            .save-icon {
                width: 16px;
                height: 16px;
                margin-right: 4px;
            }

            .start-automation-btn {
                background-color: #470CED;
                color: #FFFFFF;
                border: none;
            }

            .start-automation-btn:hover:not(:disabled) {
                background-color: #2A00A0;
            }


            .start-automation-btn:disabled {
                background-color: #cfd4d4 !important;
                color: rgba(0, 0, 0, 0.8) !important;
                cursor: not-allowed !important;
                pointer-events: none !important;
            }

            .start-automation-btn:disabled img {
                opacity: 0.8 !important;
                transition: none !important;
                filter: brightness(0) !important;
                -webkit-transition: none !important;
                -moz-transition: none !important;
                -o-transition: none !important;
            }

            .apply-icon {
                width: 16px;
                height: 16px;
                margin-right: 4px;
                opacity: 1;
                transition: none;
                filter: brightness(0) invert(1);
                -webkit-transition: none;
                -moz-transition: none;
                -o-transition: none;
            }

            #snap-bulk-upload-btn .button-text {
                font-family: "Amazon Ember";
                font-size: 14px;
                font-weight: 500;
                line-height: 1;
            }

            .reset-default-btn {
                color: #606F95 !important;
                cursor: pointer;
                font-family: "Amazon Ember";
                font-weight: 500;
                font-size: 12px;
                background: none;
                border: none;
                padding-right: 10px;
                margin-right: auto;
                transition: color 0.2s ease;
            }

            .reset-default-btn:hover {
                color: #470CED !important;
            }

            .control-group.custom-scale-control-group .control-label,
            .control-group.tumbler-custom-scale-control-group .control-label {
                display: none !important;
            }

            .control-group-custom-colors-control-group {
                display: flex;
                flex-direction: column;
                gap: 0;
                width: calc((100% - 40px) / 5);
                margin: 0;
            }

            .custom-color-input {
                display: flex;
                align-items: center;
                border: 1.5px solid #DCE0E5;
                border-radius: 4px;
                padding: 0;
                height: 40px;
                width: 100%;
                background: #FFFFFF;
            }

            .hex-prefix {
                width: 32px !important;
                min-width: 32px !important;
                max-width: 32px !important;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #DCE0E5;
                border-radius: 4px 0 0 4px;
                flex-shrink: 0 !important;
                box-sizing: border-box !important;
            }

            .hex-prefix * {
                all: unset !important;
            }

            .hex-prefix span {
                font-family: "Amazon Ember" !important;
                font-size: 14px !important;
                color: #606D85 !important;
                display: block !important;
                width: 100% !important;
                text-align: center !important;
            }

            .color-input {
                flex: 1;
                border: none;
                outline: none;
                font-family: "Amazon Ember";
                font-size: 12px;
                color: #181818;
                background: transparent;
                padding: 0px;
                height: 100%;
            }

            .color-input::placeholder {
                color: #BFC7D2 !important;
            }

            .custom-color-input:focus-within {
                border-color: #470CED !important;
                outline: none;
                box-shadow: none;
            }

            .custom-color-input:focus-within .hex-prefix {
                background: #470CED !important;
            }

            .custom-color-input:focus-within .hex-prefix span {
                color: #FFFFFF !important;
            }

            .custom-color-input.error {
                border-color: #FF391F !important;
            }

            .custom-color-input.error .hex-prefix {
                background: #FF391F !important;
            }

            .custom-color-input.error .hex-prefix span {
                color: #FFFFFF !important;
            }

            .color-input.error {
                color: #FF391F !important;
            }

            .custom-color-input.disabled {
                opacity: 0.5;
                pointer-events: none;
            }

            .donation-btn-wrapper {
                position: relative;
                overflow: visible;
                z-index: 1;
                display: inline-block;
                transition: transform 0.2s ease;
            }

            .donation-btn-wrapper:hover {
                transform: scale(1.05);
            }

            .donation-btn-wrapper[data-tooltip]:before {
                content: attr(data-tooltip);
                position: absolute;
                bottom: calc(100% + 8px);
                left: 50%;
                transform: translateX(-50%);
                padding: 4px 12px;
                background-color: #1F2937;
                color: white;
                font-size: 12px;
                border-radius: 999px;
                white-space: nowrap;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                z-index: 1000;
                pointer-events: none;
            }

            .donation-btn-wrapper[data-tooltip]:after {
                content: '';
                position: absolute;
                bottom: calc(100% + 4px);
                left: 50%;
                transform: translateX(-50%);
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #1F2937;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                z-index: 1000;
                pointer-events: none;
            }

            .donation-btn-wrapper[data-tooltip]:hover:before,
            .donation-btn-wrapper[data-tooltip]:hover:after {
                opacity: 1;
                visibility: visible;
            }

            .tooltip-wrapper {
                position: relative;
                display: flex;
                align-items: center;
            }

            .tooltip-wrapper[data-tooltip]:before {
                content: attr(data-tooltip);
                position: absolute;
                bottom: calc(100% + 8px);
                left: 50%;
                transform: translateX(-50%);
                padding: 4px 12px;
                background-color: #1F2937;
                color: white;
                font-size: 12px;
                border-radius: 999px;
                white-space: nowrap;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                z-index: 1000;
                pointer-events: none;
                font-family: "Amazon Ember";
                font-weight: 500;
            }

            .tooltip-wrapper[data-tooltip]:after {
                content: '';
                position: absolute;
                bottom: calc(100% + 4px);
                left: 50%;
                transform: translateX(-50%);
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #1F2937;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                z-index: 1000;
                pointer-events: none;
            }

            .tooltip-wrapper[data-tooltip]:hover:before,
            .tooltip-wrapper[data-tooltip]:hover:after {
                opacity: 1;
                visibility: visible;
            }

            .automate-option.text-swap .tooltip-wrapper {
                display: flex;
                align-items: center;
                width: 100%;
            }

            .tooltip-wrapper {
                position: relative;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .automate-option {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .automate-checkbox {
                margin: 0;
                width: 20px;
                height: 20px;
                flex-shrink: 0;
            }

            .automate-label {
                font-family: "Amazon Ember";
                font-weight: 500;
                font-size: 13px;
                color: #000000;
                cursor: pointer;
                user-select: none;
                margin: 0;
                display: block;
            }

            .automate-option.text-swap .tooltip-wrapper {
                display: flex;
                align-items: center;
                width: 100%;
                gap: 10px;
            }

            .automate-option.text-swap .text-container {
                display: flex;
                align-items: center;
                gap: 0;
            }

            .clear-all-btn:hover {
                background: #FF391F !important;
            }

            .clear-all-btn:hover img {
                filter: brightness(0) invert(1) !important;
            }

            .social-btn-wrapper {
                position: relative;
                overflow: visible;
                z-index: 1;
                display: inline-block;
                transition: transform 0.2s ease;
            }

            .social-btn-wrapper:hover {
                transform: scale(1.05);
            }

            .social-btn-wrapper[data-tooltip]:before {
                content: attr(data-tooltip);
                position: absolute;
                bottom: calc(100% + 8px);
                left: 50%;
                transform: translateX(-50%);
                padding: 4px 12px;
                background-color: #1F2937;
                color: white;
                font-size: 12px;
                border-radius: 999px;
                white-space: nowrap;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                z-index: 1000;
                pointer-events: none;
            }

            .social-btn-wrapper[data-tooltip]:after {
                content: '';
                position: absolute;
                bottom: calc(100% + 4px);
                left: 50%;
                transform: translateX(-50%);
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #1F2937;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                z-index: 1000;
                pointer-events: none;
            }

            .social-btn-wrapper[data-tooltip]:hover:before,
            .social-btn-wrapper[data-tooltip]:hover:after {
                opacity: 1;
                visibility: visible;
            }

            .automate-radio {
                width: 20px;
                height: 20px;
                appearance: none;
                background-color: transparent;
                border: none;
                cursor: pointer;
                transition: background-color 0.2s ease;
                margin: 0;
                flex-shrink: 0;
            }
            .automate-radio:checked {
                background-color: transparent;
                background-image: url("${chrome.runtime.getURL('assets/checkbox-ic.svg')}");
                background-size: 20px 20px;
                background-position: center;
                background-repeat: no-repeat;
            }
            .automate-radio:not(:checked) {
                background-image: url("${chrome.runtime.getURL('assets/checkbox-ic.svg')}");
                background-size: 20px 20px;
                background-position: center;
                background-repeat: no-repeat;
                filter: invert(97%) sepia(0%) saturate(0%) hue-rotate(246deg) brightness(103%) contrast(101%);
            }
            .automate-radio-label {
                font-family: "Amazon Ember";
                font-weight: 500;
                font-size: 13px;
                color: #000000;
                cursor: pointer;
                user-select: none;
                margin: 0;
                display: block;
            }
        `;
        document.head.appendChild(styleEl);

        setTimeout(() => {
            const percentPrefix = document.querySelector('.percent-prefix');
            if (percentPrefix) {
                percentPrefix.classList.add('debug');
            }
        }, 1000);

        const popupHTML = `
            <div class="snap-bulk-upload-overlay">
                <div class="snap-bulk-upload-container">
                    <div class="upload-tray-container">
                        <div class="upload-tray-header">
                            <div class="header-left" style="display: flex; align-items: center;">
                                <img src="${chrome.runtime.getURL('assets/upload-tray-ic.svg')}" alt="Upload Tray Icon" style="width: 20.76px; height: 20.76px;">
                                <h2 style="margin-right: 32px;">Upload Tray</h2>
                                <div style="display: flex; gap: 32px;">
                                    <div class="native-uploader" style="display: flex; align-items: center;">
                                        <input type="radio" id="native-uploader" name="uploader-type" class="automate-radio" style="margin: 0;">
                                        <label for="native-uploader" class="automate-radio-label" style="margin-left: 10px;">Use Native Uploader</label>
                                    </div>
                                    <div class="snap-uploader" style="display: flex; align-items: center;">
                                        <input type="radio" id="snap-uploader" name="uploader-type" class="automate-radio" checked style="margin: 0;">
                                        <label for="snap-uploader" class="automate-radio-label" style="margin-left: 10px;">Use Snap Uploader</label>
                                    </div>
                                </div>
                            </div>
                            <div class="header-right">
                                <button class="social-btn-wrapper" data-tooltip="Join Merch Momentum Group" style="background: none; border: none; padding: 0; cursor: pointer; margin-right: 10px; position: relative; overflow: visible;">
                                    <a href="https://www.facebook.com/groups/1828483987467702" target="_blank">
                                        <img src="${chrome.runtime.getURL('assets/fb-ic.svg')}" alt="Join Facebook Group" class="social-btn" style="height: 24px; width: auto;">
                                    </a>
                                </button>
                                <button class="social-btn-wrapper" data-tooltip="Join Discord Server" style="background: none; border: none; padding: 0; cursor: pointer; margin-right: 10px; position: relative; overflow: visible;">
                                    <a href="https://discord.com/invite/ZAq3MQkvNj" target="_blank">
                                        <img src="${chrome.runtime.getURL('assets/discord-ic.svg')}" alt="Join Discord Server" class="social-btn" style="height: 24px; width: auto;">
                                    </a>
                                </button>
                                <button class="social-btn-wrapper" data-tooltip="Subscribe to Philip Anders on YouTube" style="background: none; border: none; padding: 0; cursor: pointer; margin-right: 10px; position: relative; overflow: visible;">
                                    <a href="https://www.youtube.com/watch?v=zCkAg68r0S4" target="_blank">
                                        <img src="${chrome.runtime.getURL('assets/youtube-ic.svg')}" alt="Subscribe to Philip Anders on YouTube" class="social-btn" style="height: 24px; width: auto;">
                                    </a>
                                </button>
                                <button class="donation-btn-wrapper" data-tooltip="Support Snap" style="background: none; border: none; padding: 0; cursor: pointer;">
                                    <img src="${chrome.runtime.getURL('assets/bmc-button.png')}" alt="Buy Me a Coffee" class="donation-btn" style="height: 28px; width: auto;">
                                </button>
                                <img src="${chrome.runtime.getURL('assets/close-popup-ic.svg')}" alt="Close" class="header-close-icon" id="header-close-icon" style="cursor: pointer; margin-left: 16px; height: 24px; width: auto;">
                            </div>
                        </div>
                        <input type="file" id="fileInput" multiple accept="image/png" style="display: none;">
                        <div class="drag-drop-area dashed-border" id="dragDropArea">
                            <img src="${chrome.runtime.getURL('assets/drag-drop-multiple-ic.svg')}" alt="Drag and Drop Icon">
                            <div class="text-container">
                                <p class="primary-text">Drag and drop a folder or multiple PNG files here.</p>
                                <p class="secondary-text">Maximum file size: 25MB, up to 50 files allowed.</p>
                            </div>
                        </div>
                        <div class="loaded-files dashed-border" id="loadedFiles">
                            <div class="files-main">
                                <div class="files-header">
                                    <div class="files-label">
                                        <span>Loaded files</span>
                                        <div class="files-counter">
                                            <span>48 of 50</span>
                                        </div>
                                    </div>
                                    <div class="progress-info">
                                        <span class="progress-text">100%</span>
                                    </div>
                                </div>
                                <div class="snap-progress-bar">
                                    <div class="snap-progress-fill"></div>
                                </div>
                            </div>
                            <div class="clear-section">
                                <button class="clear-all-btn" data-tooltip="Clear Loaded Files">
                                    <img src="${chrome.runtime.getURL('assets/clear.svg')}" alt="Clear All">
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="products-options-container">
                        <div class="products-options-header">
                            <div class="header-left">
                                <img src="${chrome.runtime.getURL('assets/products-options-ic.svg')}" alt="Products Options Icon">
                                <h2>Products Options</h2>
                            </div>
                            <div class="tip-container">
                                <div class="tip-icon">
                                    <img src="${chrome.runtime.getURL('assets/apply.svg')}" alt="Tip Icon">
                                </div>
                                <div class="automation-tip">
                                    <span>For quick automation, use <strong>'Save Publish Settings'</strong> in Merch Create to save colors and prices.</span>
                                </div>
                            </div>
                        </div>

                        <div class="clothing-products-card">
                            <div class="clothing-products-header">
                                <div class="header-left">
                                    <div class="toggle-container">
                                        <button class="toggle-btn active">
                                            <span class="toggle-handle"></span>
                                        </button>
                                    </div>
                                    <span class="header-text">Clothing Products</span>
                                </div>
                            </div>
                            <div class="clothing-products-controls">
                                <div class="control-group shirts-control-group">
                                    <div class="product-type-label">
                                        <span>Shirts</span>
                                    </div>
                                    <span class="control-label">Sides Options:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Default (Front)</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Default (Front)</div>
                                                <div class="dropdown-item">Back</div>
                                                <div class="dropdown-item">Frontside (Pocket)</div>
                                                <div class="dropdown-item">Back & Pocket</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="control-group hoodie-control-group">
                                    <div class="product-type-label">
                                        <span>Pullover Hoodie</span>
                                    </div>
                                    <span class="control-label">Sides Options:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Default (Front)</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Default (Front)</div>
                                                <div class="dropdown-item">Back</div>
                                                <div class="dropdown-item">Frontside (Pocket)</div>
                                                <div class="dropdown-item">Back & Pocket</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="control-group zip-control-group">
                                    <div class="product-type-label">
                                        <span>ZIP Hoodie</span>
                                    </div>
                                    <span class="control-label">Sides Options:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Default (Front)</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Default (Front)</div>
                                                <div class="dropdown-item">Back</div>
                                                <div class="dropdown-item">Frontside (Pocket)</div>
                                                <div class="dropdown-item">Back & Pocket</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="control-group allproducts-colors-control-group">
                                    <div class="product-type-label">
                                        <span>All Products</span>
                                    </div>
                                    <span class="control-label">Colors:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Skip</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Skip</div>
                                                <div class="dropdown-item">Dark Colors</div>
                                                <div class="dropdown-item">Light Colors</div>
                                                <div class="dropdown-item">All Colors</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="control-group allproducts-prices-control-group">
                                    <div class="product-type-label">
                                        <span>All Products</span>
                                    </div>
                                    <span class="control-label">Prices:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Skip</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Skip</div>
                                                <div class="dropdown-item">Default Prices</div>
                                                <div class="dropdown-item">Suggested Prices</div>
                                                <div class="dropdown-item">Market Average</div>
                                                <div class="dropdown-item">Max Prices</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="scalable-products-card">
                            <div class="scalable-products-header">
                                <div class="header-left">
                                    <div class="toggle-container">
                                        <button class="toggle-btn active">
                                            <span class="toggle-handle"></span>
                                        </button>
                                    </div>
                                    <span class="header-text">Scalable Products</span>
                                </div>
                            </div>
                            <div class="scalable-products-controls">
                                <div class="control-group scale-options-control-group">
                                    <span class="control-label">Scale & Pattern Options:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Skip</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Skip</div>
                                                <div class="dropdown-item">Keep current</div>
                                                <div class="dropdown-item">Custom scale</div>
                                                <div class="dropdown-item">100%</div>
                                                <div class="dropdown-item">85%</div>
                                                <div class="dropdown-item">75%</div>
                                                <div class="dropdown-item">Pattern</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group custom-scale-control-group">
                                    <span class="control-label">Custom Scale</span>
                                    <div class="control-group-wrapper">
                                        <div class="custom-scale-input disabled">
                                            <div class="percent-prefix">
                                                <span>%</span>
                                            </div>
                                            <input type="text" class="scale-input" placeholder="50 to 100%" maxlength="3" disabled>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group color-options-control-group">
                                    <span class="control-label">Color:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <div class="header-content">
                                                <div class="color-indicator" style="background-color: #FFFFFF;"></div>
                                                <span>Default (White)</span>
                                            </div>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item">
                                                    <div class="color-indicator">
                                                        <img src="${chrome.runtime.getURL('assets/colorwheel-ic.svg')}" alt="Color Wheel">
                                                    </div>
                                                    Custom Color
                                                </div>
                                                <div class="dropdown-item selected">
                                                    <div class="color-indicator" style="background-color: #FFFFFF;"></div>
                                                    Default (White)
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #000000;"></div>
                                                    Black
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #840A08;"></div>
                                                    Dark Red
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #C70010;"></div>
                                                    Crimson
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #F36900;"></div>
                                                    Vivid Orange
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #FEC600;"></div>
                                                    Bright Yellow
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #01B62F;"></div>
                                                    Kelly Green
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #1C8C46;"></div>
                                                    Forest Green
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #37602B;"></div>
                                                    Dark Olive
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #1AB7EA;"></div>
                                                    Sky Blue
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #002BB6;"></div>
                                                    Royal Blue
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #5C2D91;"></div>
                                                    Purple
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #E0218A;"></div>
                                                    Hot Pink
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #E9CDDB;"></div>
                                                    Pale Pink
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #7B4A1B;"></div>
                                                    Brown
                                                </div>
                                                <div class="dropdown-item">
                                                    <div class="color-indicator" style="background-color: #979797;"></div>
                                                    Gray
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group-custom-colors-control-group">
                                    <div class="control-group-wrapper">
                                        <div class="custom-color-input disabled">
                                            <div class="hex-prefix">
                                                <span>#</span>
                                            </div>
                                            <input type="text" class="color-input" placeholder="Hexcode" disabled>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group price-options-control-group">
                                    <span class="control-label">Prices:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Skip</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Skip</div>
                                                <div class="dropdown-item">Default Prices</div>
                                                <div class="dropdown-item">Suggested Prices</div>
                                                <div class="dropdown-item">Market Average</div>
                                                <div class="dropdown-item">Max Prices</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tumbler-products-card">
                            <div class="tumbler-products-header">
                                <div class="header-left">
                                    <div class="toggle-container">
                                        <button class="toggle-btn active">
                                            <span class="toggle-handle"></span>
                                        </button>
                                    </div>
                                    <span class="header-text">Tumbler Product</span>
                                </div>
                            </div>
                            <div class="tumbler-products-controls">
                                <div class="control-group tumbler-sides-control-group">
                                    <span class="control-label">Sides Options:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Default (One Side)</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Default (One Side)</div>
                                                <div class="dropdown-item">Two Sides</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group tumbler-scale-control-group">
                                    <span class="control-label">Scale:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>100%</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item">Custom scale</div>
                                                <div class="dropdown-item selected">100%</div>
                                                <div class="dropdown-item">85%</div>
                                                <div class="dropdown-item">75%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group tumbler-custom-scale-control-group">
                                    <span class="control-label">Custom Scale</span>
                                    <div class="control-group-wrapper">
                                        <div class="custom-scale-input disabled">
                                            <div class="percent-prefix">
                                                <span>%</span>
                                            </div>
                                            <input type="text" class="scale-input" placeholder="50 to 100%" maxlength="3" disabled>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group tumbler-colors-control-group">
                                    <span class="control-label">Colors:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Skip</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Skip</div>
                                                <div class="dropdown-item">Dark Colors</div>
                                                <div class="dropdown-item">Light Colors</div>
                                                <div class="dropdown-item">All Colors</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group tumbler-prices-control-group">
                                    <span class="control-label">Prices:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Skip</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Skip</div>
                                                <div class="dropdown-item">Default Prices</div>
                                                <div class="dropdown-item">Suggested Prices</div>
                                                <div class="dropdown-item">Market Average</div>
                                                <div class="dropdown-item">Max Prices</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mug-products-card">
                            <div class="mug-products-header">
                                <div class="header-left">
                                    <div class="toggle-container">
                                        <button class="toggle-btn active">
                                            <span class="toggle-handle"></span>
                                        </button>
                                    </div>
                                    <span class="header-text">Mug Product</span>
                                </div>
                            </div>
                            <div class="mug-products-controls">
                                <div class="control-group mug-sides-control-group">
                                    <span class="control-label">Sides Options:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Default (One Side)</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Default (One Side)</div>
                                                <div class="dropdown-item">Two Sides</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group mug-scale-control-group">
                                    <span class="control-label">Scale:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>100%</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item">Custom scale</div>
                                                <div class="dropdown-item selected">100%</div>
                                                <div class="dropdown-item">85%</div>
                                                <div class="dropdown-item">75%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group mug-custom-scale-control-group">
                                    <span class="control-label">Custom Scale</span>
                                    <div class="control-group-wrapper">
                                        <div class="custom-scale-input disabled">
                                            <div class="percent-prefix">
                                                <span>%</span>
                                            </div>
                                            <input type="text" class="scale-input" placeholder="50 to 100%" maxlength="3" disabled>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group mug-colors-control-group">
                                    <div class="control-label-wrapper" style="display: flex; align-items: center; gap: 6px;">
                                        <span class="control-label">Color Invert:</span>
                                        <div class="tooltip-wrapper" data-tooltip="Turn your design into a dark version, optimized for Mugs (Text and silhouette art only)" style="position: relative; display: inline-flex; align-items: center;">
                                            <img src="${chrome.runtime.getURL('assets/tip-ic.svg')}" alt="Tip" style="width: 12px; height: 12px; cursor: help; margin-bottom: 4px;">
                                        </div>
                                    </div>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Original</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Original</div>
                                                <div class="dropdown-item">Invert to 100% Black</div>
                                                <div class="dropdown-item">Smart Invert to Black</div>
                                                <div class="dropdown-item" data-value="blackBackground">Black Outline</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="control-group mug-prices-control-group">
                                    <span class="control-label">Prices:</span>
                                    <div class="snap-dropdown">
                                        <div class="dropdown-header">
                                            <span>Skip</span>
                                            <img src="${chrome.runtime.getURL('assets/dropdown-ic.svg')}" alt="Dropdown">
                                        </div>
                                        <div class="dropdown-menu">
                                            <div class="dropdown-list">
                                                <div class="dropdown-item selected">Skip</div>
                                                <div class="dropdown-item">Default Prices</div>
                                                <div class="dropdown-item">Suggested Prices</div>
                                                <div class="dropdown-item">Market Average</div>
                                                <div class="dropdown-item">Max Prices</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="terms-container">
                        <div class="terms-content">
                            <div class="terms-checkbox">
                                <input type="checkbox" id="terms-checkbox" class="terms-checkbox-input">
                                <label for="terms-checkbox" class="terms-checkbox-label">
                                    You agree that originality matters, and uploading others' designs without consent is unfair and unethical. <strong>You're not a THIEF.</strong>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="automate-container">
                        <div class="automate-content">
                            <div class="automate-options">
                                <div class="automate-option">
                                    <div class="tooltip-wrapper" data-tooltip="Save listing to drafts">
                                        <input type="checkbox" id="save-to-drafts" class="automate-checkbox">
                                        <label for="save-to-drafts" class="automate-label">Save to Drafts</label>
                                    </div>
                                </div>
                                <div class="automate-option">
                                    <div class="tooltip-wrapper" data-tooltip="Copy EN to all languages">
                                        <input type="checkbox" id="copy-en-to-all" class="automate-checkbox">
                                        <label for="copy-en-to-all" class="automate-label">Copy EN to All</label>
                                    </div>
                                </div>
                                <div class="automate-option text-swap">
                                    <div class="tooltip-wrapper" data-tooltip="Place #snap in your text to be replaced with the uploaded filename">
                                        <input type="checkbox" id="auto-text-swap" class="automate-checkbox">
                                        <div class="text-container">
                                            <label for="auto-text-swap" class="automate-label">Text Swap </label>
                                            <a href="#" class="text-swap-link">
                                                <span class="text-link">How?</span>
                                                <span class="gap-spacer"></span>
                                                <img src="${chrome.runtime.getURL('assets/pdf-ic.svg')}" alt="PDF Icon" class="pdf-icon">
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="automate-buttons">
                                <button class="reset-default-btn">Reset to default</button>
                                <button id="close-btn" class="automate-btn close-btn">
                                    <img src="${chrome.runtime.getURL('assets/save-color-ic.svg')}" alt="Save Icon" class="save-icon">
                                    Save Settings
                                </button>
                                <button id="start-automation-btn" class="automate-btn start-automation-btn">
                                    <img src="${chrome.runtime.getURL('assets/apply.svg')}" alt="Apply Icon" class="apply-icon">
                                    Start Automation
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const popupEl = document.createElement('div');
        popupEl.innerHTML = popupHTML;
        document.body.appendChild(popupEl);

        initScrollControl();

        initTermsCheckbox();
        initAutomationCheckboxes();

        initResetButton();

        initCustomColorInputValidation();

        const closeBtn = document.getElementById('close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                SaveSnapSettings();
                
                // Get the icon and text elements
                const saveIcon = closeBtn.querySelector('.save-icon');
                const textNodes = Array.from(closeBtn.childNodes).filter(n => n.nodeType === Node.TEXT_NODE && n.textContent.trim());
                const buttonTextNode = textNodes.length > 0 ? textNodes[0] : null;
                
                // Store original state
                const originalIconDisplay = saveIcon ? saveIcon.style.display : '';
                const originalText = buttonTextNode ? buttonTextNode.textContent.trim() : 'Save Settings';
                
                // Hide icon and text
                if (saveIcon) {
                    saveIcon.style.display = 'none';
                }
                if (buttonTextNode) {
                    buttonTextNode.textContent = '';
                }
                
                // Create and add spinner
                const spinner = document.createElement('div');
                spinner.className = 'save-spinner';
                closeBtn.appendChild(spinner);
                
                // Add loading class to button (blue background, no hover)
                closeBtn.classList.add('loading');
                
                // Restore after 1 second
                setTimeout(() => {
                    // Remove spinner
                    if (spinner.parentNode) {
                        spinner.remove();
                    }
                    
                    // Remove loading class
                    closeBtn.classList.remove('loading');
                    
                    // Restore icon and text
                    if (saveIcon) {
                        saveIcon.style.display = originalIconDisplay || '';
                    }
                    if (buttonTextNode) {
                        buttonTextNode.textContent = originalText;
                    }
                }, 1000);
            });
        }

        const headerCloseIcon = document.getElementById('header-close-icon');
        if (headerCloseIcon) {
            headerCloseIcon.addEventListener('click', () => closePopup(false));
        }

        const overlay = document.querySelector('.snap-bulk-upload-overlay');
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {

                closePopup(false);
            }
        });

        function initLottieAnimations() {
            try {

                const buttonLottieContainer = rainbowBtn.querySelector('.lottie-icon');
                if (buttonLottieContainer) {
                    lottie.loadAnimation({
                        container: buttonLottieContainer,
                        renderer: 'svg',
                        loop: true,
                        autoplay: true,
                        path: chrome.runtime.getURL('assets/lightning-white.json'),
                        rendererSettings: {
                            progressiveLoad: true,
                            preserveAspectRatio: 'xMidYMid meet'
                        }
                    });
                }
            } catch (error) {
            }
        }

        const toggleBtns = document.querySelectorAll('.toggle-btn');

        toggleBtns.forEach(toggleBtn => {
            toggleBtn.addEventListener('click', () => {
                toggleBtn.classList.toggle('active');
                const isActive = toggleBtn.classList.contains('active');
                toggleBtn.style.backgroundColor = isActive ? '#470CED' : '#cfd4d4';

                const card = toggleBtn.closest('.clothing-products-card, .scalable-products-card, .tumbler-products-card, .mug-products-card');
                const controls = card.querySelector('.clothing-products-controls, .scalable-products-controls, .tumbler-products-controls, .mug-products-controls');

                if (!isActive) {
                    card.classList.add('off');
                    controls.classList.add('off');
                } else {
                    card.classList.remove('off');
                    controls.classList.remove('off');
                }

                updateStartAutomationButton();
            });
        });

        function initDragAndDrop() {
            const dragDropArea = document.getElementById('dragDropArea');
            const fileInput = document.getElementById('fileInput');

            function handleFiles(files) {
                const MAX_FILE_SIZE = 24 * 1024 * 1024; 
                const MAX_FILES = 50; 
                const validFiles = [];
                const skippedFiles = {
                    wrongFormat: [],
                    tooLarge: [],
                    excess: [] 
                };

                Array.from(files).forEach((file, index) => {
                    if (validFiles.length >= MAX_FILES) {
                        skippedFiles.excess.push(file.name);
                        return;
                    }

                    if (file.type === 'image/png') {
                        if (file.size <= MAX_FILE_SIZE) {
                            validFiles.push(file);
                        } else {
                            skippedFiles.tooLarge.push(file.name);
                        }
                    } else {
                        skippedFiles.wrongFormat.push(file.name);
                    }
                });

                if (skippedFiles.wrongFormat.length > 0 || skippedFiles.tooLarge.length > 0 || skippedFiles.excess.length > 0) {
                    let warningMessage = '';
                    const totalSkipped = skippedFiles.wrongFormat.length + skippedFiles.tooLarge.length + skippedFiles.excess.length;

                    if (totalSkipped === 1) {
                        warningMessage = 'One file was skipped:\n\n';
                    } else {
                        warningMessage = `${totalSkipped} files were skipped:\n\n`;
                    }

                    if (skippedFiles.wrongFormat.length > 0) {
                        if (skippedFiles.wrongFormat.length === 1) {
                            warningMessage += 'Non-PNG file (skipped):\n';
                        } else {
                            warningMessage += 'Non-PNG files (skipped):\n';
                        }
                        warningMessage += skippedFiles.wrongFormat.join('\n') + '\n\n';
                    }

                    if (skippedFiles.tooLarge.length > 0) {
                        if (skippedFiles.tooLarge.length === 1) {
                            warningMessage += 'File larger than 24MB (skipped):\n';
                        } else {
                            warningMessage += 'Files larger than 24MB (skipped):\n';
                        }
                        warningMessage += skippedFiles.tooLarge.join('\n') + '\n\n';
                    }

                    if (skippedFiles.excess.length > 0) {
                        if (skippedFiles.excess.length === 1) {
                            warningMessage += 'File exceeding 50 file limit (skipped):\n';
                        } else {
                            warningMessage += 'Files exceeding 50 file limit (skipped):\n';
                        }
                        warningMessage += skippedFiles.excess.join('\n');
                    }

                    setTimeout(() => {
                        alert(warningMessage);

                        if (validFiles.length > 0) {
                            startProgressAnimations(validFiles);
                        }
                    }, 500);
                } else if (validFiles.length > 0) {

                    startProgressAnimations(validFiles);
                }

                if (validFiles.length === 0) {

                    const dragDropArea = document.getElementById('dragDropArea');
                    dragDropArea.style.display = 'flex';
                    dragDropArea.style.alignItems = 'center';
                    dragDropArea.style.justifyContent = 'center';
                    dragDropArea.classList.remove('drag-active');
                    return;
                }

                function startProgressAnimations(validFiles) {

                    const filesSection = document.getElementById('loadedFiles');
                    const dragDropArea = document.getElementById('dragDropArea');
                    const counterSpan = filesSection.querySelector('.files-counter span');
                    const progressText = filesSection.querySelector('.progress-text');
                    const progressFill = filesSection.querySelector('.snap-progress-fill');

                    const MAX_FILES = 50;
                    const actualPercentage = Math.min(Math.round((validFiles.length / MAX_FILES) * 100), 100);

                    window.validFilesForAutomation = validFiles;

                    dragDropArea.style.display = 'none';
                    filesSection.classList.add('active');
                    counterSpan.textContent = `${validFiles.length} of ${MAX_FILES}`;
                    progressText.textContent = '0% of max files';

                    updateStartAutomationButton();

                    setTimeout(() => {
                        progressFill.classList.add('active');

                        progressFill.style.width = `${actualPercentage}%`;
                    }, 50);

                    let startTime;
                    const duration = 1000; 

                    function updateProgress(currentTime) {
                        if (!startTime) startTime = currentTime;
                        const elapsed = currentTime - startTime;
                        const progress = Math.min(elapsed / duration, 1);
                        const currentPercentage = Math.round(progress * actualPercentage);
                        progressText.textContent = `${currentPercentage}% of max files`;

                        if (progress < 1) {
                            requestAnimationFrame(updateProgress);
                        }
                    }

                    requestAnimationFrame(updateProgress);

                    const clearButton = filesSection.querySelector('.clear-all-btn');
                    clearButton.addEventListener('click', () => {
                        filesSection.classList.remove('active');
                        progressFill.classList.remove('active');

                        progressFill.style.width = '0';

                        dragDropArea.style.display = 'flex';
                        dragDropArea.style.alignItems = 'center';
                        dragDropArea.style.justifyContent = 'center';
                        dragDropArea.classList.remove('drag-active');

                        updateStartAutomationButton();

                        const textContainer = dragDropArea.querySelector('.text-container');
                        textContainer.style.display = 'flex';
                        textContainer.style.flexDirection = 'column';
                        textContainer.style.alignItems = 'flex-start';
                        const primaryText = textContainer.querySelector('.primary-text');
                        const secondaryText = textContainer.querySelector('.secondary-text');
                        primaryText.textContent = 'Drag and drop a folder or multiple PNG files here, or paste (Ctrl/CMD+V).';
                        secondaryText.textContent = 'Maximum file size: 25MB, up to 50 files allowed.';

                        primaryText.style.textAlign = 'left';
                        secondaryText.style.textAlign = 'left';

                        const fileInput = document.getElementById('fileInput');
                        if (fileInput) {
                            fileInput.value = '';
                        }
                        window.validFilesForAutomation = null;
                    });
                }

                return validFiles;
            }

            dragDropArea.setAttribute('tabindex', '0');

            dragDropArea.addEventListener('focus', () => {
                dragDropArea.classList.add('drag-active');
            });

            dragDropArea.addEventListener('blur', () => {
                dragDropArea.classList.remove('drag-active');
            });

            dragDropArea.addEventListener('paste', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const items = e.clipboardData.items;
                const files = [];
                let processedItems = 0;

                function processItem(item) {
                    return new Promise(resolve => {
                        if (item.kind === 'file') {
                            const file = item.getAsFile();
                            if (file) {
                                files.push(file);
                            }
                        }
                        processedItems++;
                        resolve();
                    });
                }

                const promises = [];
                for (let i = 0; i < items.length; i++) {
                    promises.push(processItem(items[i]));
                }

                Promise.all(promises).then(() => {
                    if (files.length > 0) {
                        handleFiles(files);
                    }
                });
            });

            dragDropArea.addEventListener('click', () => {
                fileInput.click();

                dragDropArea.focus();
            });

            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });

            dragDropArea.addEventListener('dragenter', (e) => {
                e.preventDefault();
                dragDropArea.classList.add('drag-active');
            });

            dragDropArea.addEventListener('dragover', (e) => {
                e.preventDefault();
            });

            dragDropArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                if (!dragDropArea.contains(e.relatedTarget)) {
                    dragDropArea.classList.remove('drag-active');
                }
            });

            dragDropArea.addEventListener('drop', (e) => {
                e.preventDefault();
                dragDropArea.classList.remove('drag-active');

                const items = e.dataTransfer.items;
                const files = [];
                let processedItems = 0;
                const totalItems = items.length;

                function traverseFileTree(item, path) {
                    return new Promise(resolve => {
                        if (item.isFile) {
                            item.file(file => {
                                processedItems++;
                                files.push(file);
                                resolve();
                            });
                        } else if (item.isDirectory) {
                            const dirReader = item.createReader();
                            dirReader.readEntries(entries => {
                                const subPromises = entries.map(entry => traverseFileTree(entry, path + item.name + "/"));
                                Promise.all(subPromises).then(() => {
                                    processedItems++;
                                    resolve();
                                });
                            });
                        } else {
                            processedItems++;
                            resolve();
                        }
                    });
                }

                const promises = [];
                for (let i = 0; i < items.length; i++) {
                    const item = items[i].webkitGetAsEntry();
                    if (item) {
                        promises.push(traverseFileTree(item, ""));
                    }
                }

                Promise.all(promises).then(() => {
                    handleFiles(files);
                });
            });
        }

        initDragAndDrop();

        const rainbowBtn = document.createElement("button");
        rainbowBtn.id = "snap-bulk-upload-btn";

        const lottieContainer = document.createElement('div');
        lottieContainer.className = 'lottie-icon';

        const textSpan = document.createElement('span');
        textSpan.className = 'button-text';
        textSpan.textContent = "Snap Bulk Upload";

        rainbowBtn.appendChild(lottieContainer);
        rainbowBtn.appendChild(textSpan);

        let isPopupActive = false;

        rainbowBtn.addEventListener('click', () => {

            loadSnapSettings();

            const overlay = document.querySelector('.snap-bulk-upload-overlay');
            overlay.classList.remove('closing');
            overlay.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            document.body.style.height = '100%';
            overlay.offsetHeight;
            overlay.classList.add('show');
            isPopupActive = true;

            setTimeout(() => {
                initDonationButton();
            }, 100);

            setTimeout(() => {
                initColorDropdown();

                injectColorIndicator();

                initDropdownHandlers();
            }, 200);

            const dragDropArea = document.getElementById('dragDropArea');
            const textContainer = dragDropArea.querySelector('.text-container');
            const primaryText = textContainer.querySelector('.primary-text');
            const secondaryText = textContainer.querySelector('.secondary-text');
            primaryText.textContent = 'Drag and drop a folder or multiple PNG files here, or paste (Ctrl/CMD+V).';
            secondaryText.textContent = 'Maximum file size: 25MB, up to 50 files allowed.';

            updateStartAutomationButton();
        });

        document.addEventListener('paste', async (e) => {
            if (!isPopupActive) return; 

            const dragDropArea = document.getElementById('dragDropArea');
            if (!dragDropArea) return;

            const activeElement = document.activeElement;
            const isInputActive = activeElement.tagName === 'INPUT' || 
                                 activeElement.tagName === 'TEXTAREA' ||
                                 activeElement.isContentEditable;

            const items = e.clipboardData.items;
            const hasFiles = Array.from(items).some(item => item.kind === 'file');

            if (hasFiles) {
                e.preventDefault(); 
                e.stopPropagation();

                const files = [];
                const promises = [];

                async function processEntry(entry) {
                    if (entry.isFile) {
                        return new Promise(resolve => {
                            entry.file(file => {
                                files.push(file);
                                resolve();
                            });
                        });
                    } else if (entry.isDirectory) {
                        const reader = entry.createReader();
                        return new Promise(resolve => {
                            reader.readEntries(async entries => {
                                const subPromises = entries.map(entry => processEntry(entry));
                                await Promise.all(subPromises);
                                resolve();
                            });
                        });
                    }
                }

                for (let i = 0; i < items.length; i++) {
                    const item = items[i];
                    if (item.kind === 'file') {

                        const entry = item.webkitGetAsEntry?.() || item.getAsEntry?.();
                        if (entry) {
                            promises.push(processEntry(entry));
                        } else {

                            const file = item.getAsFile();
                            if (file) {
                                files.push(file);
                            }
                        }
                    }
                }

                try {
                    await Promise.all(promises);

                    if (files.length > 0) {

                        const dataTransfer = new DataTransfer();
                        files.forEach(file => {
                            try {
                                dataTransfer.items.add(file);
                            } catch (err) {
                            }
                        });

                        const fileInput = document.getElementById('fileInput');
                        if (fileInput) {
                            try {
                                fileInput.files = dataTransfer.files;

                                const event = new Event('change', { bubbles: true });
                                fileInput.dispatchEvent(event);
                            } catch (err) {

                                handleFiles(files);
                            }
                        } else {
                            handleFiles(files);
                        }
                    }
                } catch (err) {
                }
            }

        });

        document.addEventListener('keydown', (e) => {
            if (!isPopupActive) return;
        });

        document.body.appendChild(rainbowBtn);

        initLottieAnimations();

        document.querySelectorAll('.snap-dropdown').forEach(dropdown => {
            const header = dropdown.querySelector('.dropdown-header');
            const menu = dropdown.querySelector('.dropdown-menu');
            const items = dropdown.querySelectorAll('.dropdown-item');

            header.addEventListener('click', (e) => {
                e.stopPropagation();
                const isOpen = menu.classList.contains('show');

                document.querySelectorAll('.snap-dropdown .dropdown-menu.show').forEach(m => {
                    if (m !== menu) {
                        m.classList.remove('show');
                        m.closest('.snap-dropdown').classList.remove('focused');
                    }
                });

                menu.classList.toggle('show');
                dropdown.classList.toggle('focused');
            });

            items.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.stopPropagation();

                    if(item.classList.contains('selected')) {
                        return;
                    }

                    const value = item.textContent.trim();
                    header.querySelector('span').textContent = value;

                    items.forEach(i => i.classList.remove('selected'));
                    item.classList.add('selected');

                    menu.classList.remove('show');
                    dropdown.classList.remove('focused');

                    if (dropdown.closest('.scale-options-control-group')) {
                        handleScaleOptionChange(value, dropdown);
                    } else if (dropdown.closest('.color-options-control-group')) {
                        handleColorOptionChange(value, dropdown);
                    } else if (dropdown.closest('.tumbler-scale-control-group')) {
                        updateTumblerScaleOptions(value);
                    } else if (dropdown.closest('.tumbler-sides-control-group')) {

                        const scaleDropdown = document.querySelector('.tumbler-scale-control-group .snap-dropdown');
                        const scaleDropdownList = scaleDropdown.querySelector('.dropdown-list');
                        const scaleHeader = scaleDropdown.querySelector('.dropdown-header span');

                        if (value === 'Two Sides') {

                            if (!scaleDropdownList.querySelector('.dropdown-item:last-child').textContent.includes('Pattern')) {
                                const patternItem = document.createElement('div');
                                patternItem.className = 'dropdown-item';
                                patternItem.textContent = 'Pattern';

                                patternItem.addEventListener('click', (e) => {
                                    e.stopPropagation();

                                    scaleHeader.textContent = 'Pattern';

                                    scaleDropdownList.querySelectorAll('.dropdown-item').forEach(i => i.classList.remove('selected'));
                                    patternItem.classList.add('selected');

                                    const scaleDropdownMenu = scaleDropdownList.closest('.dropdown-menu');
                                    scaleDropdownMenu.classList.remove('show');
                                    scaleDropdownMenu.closest('.snap-dropdown').classList.remove('focused');

                                    const customScaleInput = document.querySelector('.tumbler-products-card .tumbler-custom-scale-control-group .custom-scale-input');
                                    if (customScaleInput) {
                                        customScaleInput.classList.add('disabled');
                                        const scaleInput = customScaleInput.querySelector('.scale-input');
                                        scaleInput.disabled = true;
                                        scaleInput.value = '';
                                        customScaleInput.classList.remove('error');
                                        scaleInput.classList.remove('error');

                                        customScaleInput.style.borderColor = '#DCE0E5';
                                        customScaleInput.querySelector('.percent-prefix').style.backgroundColor = '#DCE0E5';
                                        customScaleInput.querySelector('.percent-prefix span').style.color = '#606D85';
                                    }

                                    updateStartAutomationButton();
                                });

                                scaleDropdownList.appendChild(patternItem);
                            }
                        } else {

                            const patternItem = scaleDropdownList.querySelector('.dropdown-item:last-child');
                            if (patternItem && patternItem.textContent.includes('Pattern')) {
                                if (patternItem.classList.contains('selected')) {

                                    const noChangeItem = scaleDropdownList.querySelector('.dropdown-item');
                                    if (noChangeItem) {
                                        noChangeItem.classList.add('selected');
                                        scaleHeader.textContent = noChangeItem.textContent;
                                    }
                                }
                                patternItem.remove();
                            }
                        }
                    }

                    updateStartAutomationButton();
                });
            });
        });

        document.addEventListener('click', (e) => {

            const isDropdownClick = e.target.closest('.snap-dropdown');

            if (!isDropdownClick) {
                document.querySelectorAll('.snap-dropdown .dropdown-menu.show').forEach(menu => {
                    menu.classList.remove('show');
                    menu.closest('.snap-dropdown').classList.remove('focused');
                });
            }
        });

        const colorDropdown = document.querySelector('.color-options-control-group .snap-dropdown');
        if (colorDropdown) {
            const header = colorDropdown.querySelector('.dropdown-header');
            const menu = colorDropdown.querySelector('.dropdown-menu');
            const items = colorDropdown.querySelectorAll('.dropdown-item');

            const headerContent = header.querySelector('.header-content');
            const defaultItem = colorDropdown.querySelector('.dropdown-item.selected');
            if (defaultItem) {
                const colorIndicator = defaultItem.querySelector('.color-indicator').cloneNode(true);
                headerContent.innerHTML = '';
                headerContent.appendChild(colorIndicator);
                const textSpan = document.createElement('span');
                textSpan.textContent = defaultItem.textContent.trim();
                headerContent.appendChild(textSpan);
            }

            header.addEventListener('click', (e) => {
                e.stopPropagation();

                document.querySelectorAll('.snap-dropdown .dropdown-menu.show').forEach(m => {
                    if (m !== menu) {
                        m.classList.remove('show');
                        m.closest('.snap-dropdown').classList.remove('focused');
                    }
                });

                menu.classList.toggle('show');
                colorDropdown.classList.toggle('focused');
            });

            items.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.stopPropagation();

                    if(item.classList.contains('selected')) {
                        return;
                    }

                    const selectedColorIndicator = item.querySelector('.color-indicator');
                    const value = item.textContent.trim();
                    const headerContent = header.querySelector('.header-content');
                    const headerColorIndicator = headerContent.querySelector('.color-indicator');

                    if (value === 'Custom Color') {
                        headerColorIndicator.innerHTML = '<img src="' + chrome.runtime.getURL('assets/colorwheel-ic.svg') + '" alt="Color Wheel">';
                        headerColorIndicator.style.backgroundColor = '';
                    } else {
                        headerColorIndicator.innerHTML = '';
                        headerColorIndicator.style.backgroundColor = selectedColorIndicator.style.backgroundColor;
                    }

                    headerContent.querySelector('span').textContent = value;

                    items.forEach(i => i.classList.remove('selected'));
                    item.classList.add('selected');

                    menu.classList.remove('show');
                    colorDropdown.classList.remove('focused');

                    handleColorOptionChange(value, colorDropdown);

                    updateStartAutomationButton();
                });
            });
        }

        const automationTip = document.querySelector('.automation-tip span');
        let messageRotationInterval;
        let isAnimating = false; 

        if (automationTip) {

            const messages = [
                `For quick automation, use <strong>'Save Publish Settings'</strong> in Merch Create to save colors and prices.`,
                `Your designs will be uploaded with default settings when products options are turned off.`,
                `Your designs will be uploaded, compressed, and converted to 4500x5400px with <strong>Snap Uploader</strong>.`,
                `When automation starts, feel free to browse in another window, but <strong>DO NOT MINIMIZE</strong> the automation window.`,
                `Due to browser restrictions, the automation window must remain at least partially visible when you're multi-tasking.`
            ];

            let currentMessageIndex = 0;

            const tipContainer = automationTip.closest('.automation-tip');
            if (tipContainer) {
                tipContainer.style.display = 'flex';
                tipContainer.style.alignItems = 'center';
                tipContainer.style.background = 'rgba(0, 122, 255, 0.1)';
                tipContainer.style.borderRadius = '4px';
                tipContainer.style.padding = '4px 0';
                tipContainer.style.marginLeft = '10px';
                tipContainer.style.transition = 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                tipContainer.style.overflow = 'hidden'; 
            }

            automationTip.style.position = 'relative';
            automationTip.style.display = 'inline-block';
            automationTip.style.padding = '0 10px';
            automationTip.style.margin = '0';
            automationTip.style.whiteSpace = 'nowrap';
            automationTip.style.overflow = 'hidden'; 
            automationTip.style.boxSizing = 'border-box';

            function measureText(text) {
                const tempSpan = document.createElement('span');
                tempSpan.style.visibility = 'hidden';
                tempSpan.style.position = 'absolute';
                tempSpan.style.whiteSpace = 'nowrap';
                tempSpan.style.font = window.getComputedStyle(automationTip).font;
                tempSpan.style.display = 'inline-block';
                tempSpan.innerHTML = text;
                document.body.appendChild(tempSpan);
                const width = tempSpan.offsetWidth;
                document.body.removeChild(tempSpan);
                return width + 20; 
            }

            function updateMessage() {

                if (isAnimating) return;
                isAnimating = true;

                currentMessageIndex = (currentMessageIndex + 1) % messages.length;

                automationTip.style.transition = 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1)';

                automationTip.style.transform = 'translateX(-10px)';
                automationTip.style.opacity = '0';

                setTimeout(() => {

                    automationTip.style.transition = 'none';
                    automationTip.style.transform = 'translateX(10px)';
                    automationTip.innerHTML = messages[currentMessageIndex];

                    automationTip.offsetHeight;

                    if (tipContainer) {
                        const width = measureText(messages[currentMessageIndex]);
                        tipContainer.style.width = `${width}px`;
                    }

                    automationTip.style.transition = 'transform 0.3s cubic-bezier(0.0, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.0, 0, 0.2, 1)';
                    automationTip.style.transform = 'translateX(0)';
                    automationTip.style.opacity = '1';

                    setTimeout(() => {
                        isAnimating = false;
                    }, 400);
                }, 400);
            }

            automationTip.innerHTML = messages[0];
            automationTip.style.transform = 'translateX(0)';
            automationTip.style.transition = 'transform 0.3s cubic-bezier(0.0, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.0, 0, 0.2, 1)';
            automationTip.style.fontWeight = '500';

            if (tipContainer) {
                const width = measureText(messages[0]);
                tipContainer.style.width = `${width}px`;
            }

            rainbowBtn.addEventListener('click', () => {

                if (messageRotationInterval) {
                    clearInterval(messageRotationInterval);
                }

                currentMessageIndex = 0;
                automationTip.innerHTML = messages[0];
                automationTip.style.opacity = '1';
                automationTip.style.transform = 'translateX(0)';
                isAnimating = false; 

                if (tipContainer) {
                    const width = measureText(messages[0]);
                    tipContainer.style.width = `${width}px`;
                }

                messageRotationInterval = setInterval(() => {

                    if (!isAnimating) {
                        updateMessage();
                    }
                }, 7000);
            });

            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    clearInterval(messageRotationInterval);
                    messageRotationInterval = null; 
                    isAnimating = false; 

                    automationTip.style.opacity = '1';
                    automationTip.style.transform = 'translateX(0)';
                }
            });
        }

        const customScaleInput = document.querySelector('.scale-input');
        if (customScaleInput) {
            customScaleInput.addEventListener('input', (e) => {
                const value = e.target.value;
                const container = e.target.closest('.custom-scale-input');

                let cleanValue = value.replace(/[^0-9]/g, '');

                if (cleanValue.length > 0) {
                    cleanValue = parseInt(cleanValue, 10).toString();
                }

                if (parseInt(cleanValue) > 100) {
                    cleanValue = cleanValue.slice(0, -1); 
                }

                e.target.value = cleanValue;

                if (cleanValue.length === 1 && ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'].includes(cleanValue)) {
                    container.classList.add('error');
                    e.target.classList.add('error');

                    updateStartAutomationButton();
                } else if (cleanValue.length >= 2) {
                    const numericValue = parseInt(cleanValue);
                    if (numericValue < 50) {
                        container.classList.add('error');
                        e.target.classList.add('error');

                        updateStartAutomationButton();
                    } else {
                        const wasInError = container.classList.contains('error');
                        container.classList.remove('error');
                        e.target.classList.remove('error');

                        if (wasInError) {
                            updateStartAutomationButton();
                        }
                    }
                } else {
                    const wasInError = container.classList.contains('error');
                    container.classList.remove('error');
                    e.target.classList.remove('error');

                    if (wasInError) {
                        updateStartAutomationButton();
                    }
                }

                if (container.classList.contains('error')) {
                    container.style.borderColor = '#FF391F';
                    container.querySelector('.percent-prefix').style.backgroundColor = '#FF391F';
                    container.querySelector('.percent-prefix span').style.color = '#FFFFFF';
                } else {
                    container.style.borderColor = '#DCE0E5';
                    container.querySelector('.percent-prefix').style.backgroundColor = '#DCE0E5';
                    container.querySelector('.percent-prefix span').style.color = '#606D85';
                }

                updateStartAutomationButton();
            });

            customScaleInput.addEventListener('blur', (e) => {
                const container = e.target.closest('.custom-scale-input');
                if (!e.target.value) {
                    container.classList.add('error');
                    e.target.classList.add('error');
                    container.style.borderColor = '#FF391F';
                    container.querySelector('.percent-prefix').style.backgroundColor = '#FF391F';
                    container.querySelector('.percent-prefix span').style.color = '#FFFFFF';
                }

                updateStartAutomationButton();
            });
        }

        initTumblerCustomScaleInput();
        const buttonStyle = document.createElement('style');
        buttonStyle.textContent = `
            .color-wheel-btn:focus {
                outline: none;
                box-shadow: none;
            }

            }
        `;
        const colorDropdownList = document.querySelector('.color-options-control-group .dropdown-list');
        if (colorDropdownList) {
            colorDropdownList.innerHTML = `
                <div class="dropdown-item">
                    <div class="color-indicator">
                        <img src="${chrome.runtime.getURL('assets/colorwheel-ic.svg')}" alt="Color Wheel" style="width: 10px; height: 10px;">
                    </div>
                    Custom Color
                </div>
                <div class="dropdown-item selected">
                    <div class="color-indicator" style="background-color: #FFFFFF;"></div>
                    Default (White)
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #000000;"></div>
                    Black
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #840A08;"></div>
                    Dark Red
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #C70010;"></div>
                    Crimson
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #F36900;"></div>
                    Vivid Orange
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #FEC600;"></div>
                    Bright Yellow
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #01B62F;"></div>
                    Kelly Green
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #1C8C46;"></div>
                    Forest Green
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #37602B;"></div>
                    Dark Olive
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #1AB7EA;"></div>
                    Sky Blue
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #002BB6;"></div>
                    Royal Blue
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #5C2D91;"></div>
                    Purple
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #E0218A;"></div>
                    Hot Pink
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #E9CDDB;"></div>
                    Pale Pink
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #7B4A1B;"></div>
                    Brown
                </div>
                <div class="dropdown-item">
                    <div class="color-indicator" style="background-color: #979797;"></div>
                    Gray
                </div>
            `;
        }

        styleEl.textContent += `

            .color-options-control-group .color-indicator {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                border: 0.5px solid #E5E5E5;  
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                filter: none;
            }

            .color-options-control-group .dropdown-header {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .snap-dropdown .dropdown-item {
                font-family: "Amazon Ember";
                font-size: 12px !important;
                font-weight: 400 !important;
                color: #181818;
            }

            .snap-dropdown .dropdown-item.selected {
                font-weight: 700 !important;
                color: #470CED !important;
            }

            .color-options-control-group .dropdown-item {
                font-size: 12px !important;
                font-weight: 400 !important;
            }

            .color-options-control-group .dropdown-item.selected {
                font-weight: 700 !important;
                color: #470CED !important;
            }
        `;

        const scaleColorDropdown = document.querySelector('.color-options-control-group .snap-dropdown');
        if (scaleColorDropdown) {
            const dropdownHeader = scaleColorDropdown.querySelector('.dropdown-header');
            const dropdownMenu = scaleColorDropdown.querySelector('.dropdown-menu');

            dropdownMenu.innerHTML = `
                <div class="dropdown-list">
                    <div class="dropdown-item">
                        <div class="color-indicator">
                            <img src="${chrome.runtime.getURL('assets/colorwheel-ic.svg')}" alt="Color Wheel">
                        </div>
                        Custom Color
                    </div>
                    <div class="dropdown-item selected">
                        <div class="color-indicator" style="background-color: #FFFFFF;"></div>
                        Default (White)
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #000000;"></div>
                        Black
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #840A08;"></div>
                        Dark Red
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #C70010;"></div>
                        Crimson
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #F36900;"></div>
                        Vivid Orange
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #FEC600;"></div>
                        Bright Yellow
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #01B62F;"></div>
                        Kelly Green
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #1C8C46;"></div>
                        Forest Green
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #37602B;"></div>
                        Dark Olive
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #1AB7EA;"></div>
                        Sky Blue
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #002BB6;"></div>
                        Royal Blue
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #5C2D91;"></div>
                        Purple
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #E0218A;"></div>
                        Hot Pink
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #E9CDDB;"></div>
                        Pale Pink
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #7B4A1B;"></div>
                        Brown
                    </div>
                    <div class="dropdown-item">
                        <div class="color-indicator" style="background-color: #979797;"></div>
                        Gray
                    </div>
                </div>
            `;

            styleEl.textContent += `

                .color-options-control-group .dropdown-menu {
                    max-height: 300px;
                    overflow-y: auto;
                }

                .color-options-control-group .dropdown-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 8px 12px;
                    font-family: "Amazon Ember";
                    font-weight: 500;
                    font-size: 12px;
                    color: #181818;
                    transition: background-color 0.2s ease;

                .color-options-control-group .color-indicator {
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    border: 0.5px solid #E5E5E5;
                    flex-shrink: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    filter: none;
                }

                .color-options-control-group .color-indicator img {
                    width: 10px;
                    height: 10px;
                    display: block;
                }

                .color-options-control-group .dropdown-item.selected {
                    font-weight: 700 !important;
                    color: #470CED !important;
                    background-color: transparent;
                }

                .color-options-control-group .dropdown-item:hover {
                    background-color: #F3F4F6;
                }
            `;

            dropdownHeader.addEventListener('click', (e) => {
                e.stopPropagation();
                const isOpen = dropdownMenu.classList.contains('show');

                document.querySelectorAll('.snap-dropdown .dropdown-menu.show').forEach(m => {
                    if (m !== dropdownMenu) {
                        m.classList.remove('show');
                        m.closest('.snap-dropdown').classList.remove('focused');
                    }
                });

                dropdownMenu.classList.toggle('show');
                scaleColorDropdown.classList.toggle('focused');
            });

            const dropdownItems = dropdownMenu.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.stopPropagation();

                    if(item.classList.contains('selected')) {
                        return;
                    }

                    const selectedColorIndicator = item.querySelector('.color-indicator');
                    const value = item.textContent.trim();
                    const headerContent = dropdownHeader.querySelector('.header-content');
                    const headerColorIndicator = headerContent.querySelector('.color-indicator');

                    if (value === 'Custom Color') {
                        headerColorIndicator.innerHTML = '<img src="' + chrome.runtime.getURL('assets/colorwheel-ic.svg') + '" alt="Color Wheel">';
                        headerColorIndicator.style.backgroundColor = '';
                    } else {
                        headerColorIndicator.innerHTML = '';
                        headerColorIndicator.style.backgroundColor = selectedColorIndicator.style.backgroundColor;
                    }

                    headerContent.querySelector('span').textContent = value;

                    dropdownItems.forEach(i => i.classList.remove('selected'));
                    item.classList.add('selected');

                    dropdownMenu.classList.remove('show');
                    scaleColorDropdown.classList.remove('focused');

                    handleColorOptionChange(value, colorDropdown);

                    updateStartAutomationButton();
                });
            });
        }

        const tumblerCustomScaleInput = document.querySelector('.tumbler-products-card .tumbler-custom-scale-control-group .scale-input');
        if (tumblerCustomScaleInput) {
            tumblerCustomScaleInput.addEventListener('input', (e) => {
                const value = e.target.value;
                const container = e.target.closest('.custom-scale-input');

                let cleanValue = value.replace(/[^0-9]/g, '');

                if (cleanValue.length > 0) {
                    cleanValue = parseInt(cleanValue, 10).toString();
                }

                if (parseInt(cleanValue) > 100) {
                    cleanValue = cleanValue.slice(0, -1); 
                }

                e.target.value = cleanValue;

                if (cleanValue.length === 1 && ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'].includes(cleanValue)) {
                    container.classList.add('error');
                    e.target.classList.add('error');

                    updateStartAutomationButton();
                } else if (cleanValue.length >= 2) {
                    const numericValue = parseInt(cleanValue);
                    if (numericValue < 50) {
                        container.classList.add('error');
                        e.target.classList.add('error');

                        updateStartAutomationButton();
                    } else {
                        const wasInError = container.classList.contains('error');
                        container.classList.remove('error');
                        e.target.classList.remove('error');

                        if (wasInError) {
                            updateStartAutomationButton();
                        }
                    }
                } else {
                    const wasInError = container.classList.contains('error');
                    container.classList.remove('error');
                    e.target.classList.remove('error');

                    if (wasInError) {
                        updateStartAutomationButton();
                    }
                }

                if (container.classList.contains('error')) {
                    container.style.borderColor = '#FF391F';
                    container.querySelector('.percent-prefix').style.backgroundColor = '#FF391F';
                    container.querySelector('.percent-prefix span').style.color = '#FFFFFF';
                } else {
                    container.style.borderColor = '#DCE0E5';
                    container.querySelector('.percent-prefix').style.backgroundColor = '#DCE0E5';
                    container.querySelector('.percent-prefix span').style.color = '#606D85';
                }

                updateStartAutomationButton();
            });

            tumblerCustomScaleInput.addEventListener('blur', (e) => {
                const container = e.target.closest('.custom-scale-input');
                if (!e.target.value) {
                    container.classList.add('error');
                    e.target.classList.add('error');
                    container.style.borderColor = '#FF391F';
                    container.querySelector('.percent-prefix').style.backgroundColor = '#FF391F';
                    container.querySelector('.percent-prefix span').style.color = '#FFFFFF';
                }

                updateStartAutomationButton();
            });
        }
    }

    function initTumblerCustomScaleInput() {
        const tumblerCustomScaleInput = document.querySelector('.tumbler-products-card .tumbler-custom-scale-control-group .scale-input');
        if (tumblerCustomScaleInput) {
            tumblerCustomScaleInput.addEventListener('input', (e) => {
                const value = e.target.value;
                const container = e.target.closest('.custom-scale-input');

                let cleanValue = value.replace(/[^0-9]/g, '');

                if (cleanValue.length > 0) {
                    cleanValue = parseInt(cleanValue, 10).toString();
                }

                if (parseInt(cleanValue) > 100) {
                    cleanValue = cleanValue.slice(0, -1); 
                }

                e.target.value = cleanValue;

                if (cleanValue.length === 1 && ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'].includes(cleanValue)) {
                    container.classList.add('error');
                    e.target.classList.add('error');

                    updateStartAutomationButton();
                } else if (cleanValue.length >= 2) {
                    const numericValue = parseInt(cleanValue);
                    if (numericValue < 50) {
                        container.classList.add('error');
                        e.target.classList.add('error');

                        updateStartAutomationButton();
                    } else {
                        const wasInError = container.classList.contains('error');
                        container.classList.remove('error');
                        e.target.classList.remove('error');

                        if (wasInError) {
                            updateStartAutomationButton();
                        }
                    }
                } else {
                    const wasInError = container.classList.contains('error');
                    container.classList.remove('error');
                    e.target.classList.remove('error');

                    if (wasInError) {
                        updateStartAutomationButton();
                    }
                }

                if (container.classList.contains('error')) {
                    container.style.borderColor = '#FF391F';
                    container.querySelector('.percent-prefix').style.backgroundColor = '#FF391F';
                    container.querySelector('.percent-prefix span').style.color = '#FFFFFF';
                } else {
                    container.style.borderColor = '#DCE0E5';
                    container.querySelector('.percent-prefix').style.backgroundColor = '#DCE0E5';
                    container.querySelector('.percent-prefix span').style.color = '#606D85';
                }

                updateStartAutomationButton();
            });

            tumblerCustomScaleInput.addEventListener('blur', (e) => {
                const container = e.target.closest('.custom-scale-input');
                if (!e.target.value) {
                    container.classList.add('error');
                    e.target.classList.add('error');
                    container.style.borderColor = '#FF391F';
                    container.querySelector('.percent-prefix').style.backgroundColor = '#FF391F';
                    container.querySelector('.percent-prefix span').style.color = '#FFFFFF';
                }

                updateStartAutomationButton();
            });
        }
    }

    function handlePageState() {
        if (isDesignsPage()) {
            initAutomation();
        } else {
            cleanupAutomation();
        }
    }

    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", handlePageState);
    } else {
        handlePageState();
    }

    {
        let lastUrl = window.location.href;

        setInterval(() => {
            if (window.location.href !== lastUrl) {
                lastUrl = window.location.href;
                handlePageState();
            }
        }, 1000);

        const pushState = history.pushState;
        history.pushState = function(...args) {
            pushState.apply(this, args);
            window.dispatchEvent(new Event('locationchange'));
        };

        const replaceState = history.replaceState;
        history.replaceState = function(...args) {
            replaceState.apply(this, args);
            window.dispatchEvent(new Event('locationchange'));
        };

        window.addEventListener('locationchange', handlePageState);
    }

    const saveToDraftsCheckbox = document.getElementById('save-to-drafts');
    if (saveToDraftsCheckbox) {
        saveToDraftsCheckbox.addEventListener('change', handleSaveToDrafts);
    }

    const copyEnToAllCheckbox = document.getElementById('copy-en-to-all');
    if (copyEnToAllCheckbox) {
        copyEnToAllCheckbox.addEventListener('change', handleCopyEnToAll);
    }

    const autoTextSwapCheckbox = document.getElementById('auto-text-swap');
    if (autoTextSwapCheckbox) {
        autoTextSwapCheckbox.addEventListener('change', handleAutoTextSwap);
    }

    function handleSaveToDrafts(e) {

    }

    function handleCopyEnToAll(e) {

    }

    function handleAutoTextSwap(e) {

    }

    const startAutomationBtn = document.getElementById('start-automation-btn');
    if (startAutomationBtn) {
        startAutomationBtn.addEventListener('click', startAutomation);
    }

function startAutomation() {

    const loadedFiles = document.getElementById('loadedFiles');
    if (!loadedFiles || !loadedFiles.classList.contains('active')) {
        return;
    }

    if (!window.validFilesForAutomation || window.validFilesForAutomation.length === 0) {
        return;
    }

    const MAX_BYTES = 25 * 1024 * 1024;
    const acceptedFiles = window.validFilesForAutomation.filter(f => (typeof f.size === 'number' ? f.size <= MAX_BYTES : true));
    const rejectedFiles = window.validFilesForAutomation.filter(f => (typeof f.size === 'number' ? f.size > MAX_BYTES : false));
    if (rejectedFiles.length) {

    }

    const files = acceptedFiles.map((file, index) => ({
        filename: file.name,
        index: index,
        file: file
    }));

    Promise.all(files.map(fileObj => {
        return new Promise((resolve) => {

            const blobUrl = URL.createObjectURL(fileObj.file);
            resolve({
                filename: fileObj.filename,
                blobUrl: blobUrl,
                index: fileObj.index
            });
        });
    }))
    .then(processedFiles => {

        const settings = {
            clothingProducts: getClothingProductSettings(),
            scalableProducts: getScalableProductSettings(),
            tumblerProduct: getTumblerProductSettings(),
            mugProducts: getMugProductSettings(),
            actions: {
                saveToDrafts: document.getElementById('save-to-drafts')?.checked || false,
                copyEnToAll: document.getElementById('copy-en-to-all')?.checked || false,
                textSwap: document.getElementById('auto-text-swap')?.checked || false
            },
            useNativeUploader: document.getElementById('native-uploader')?.checked || false
        };



        chrome.runtime.sendMessage({
            action: 'startAutomation',
            files: processedFiles,
            settings: settings
        }, (response) => {
            if (response && response.success) {

                setTimeout(() => {
                    const clearButton = document.querySelector('.clear-all-btn');
                    if (clearButton) {
                        clearButton.click();
                    }

                    setTimeout(() => {
                        closePopup(false);
                    }, 500);
                }, 500);
            } else {

                const startAutomationBtn = document.getElementById('start-automation-btn');
                if (startAutomationBtn) {
                    startAutomationBtn.disabled = false;
                    startAutomationBtn.textContent = 'Start Automation';
                }
                alert('Failed to start automation. Please try again.');
            }
        });
    })
    .catch(error => {

        const startAutomationBtn = document.getElementById('start-automation-btn');
        if (startAutomationBtn) {
            startAutomationBtn.disabled = false;
            startAutomationBtn.textContent = 'Start Automation';
        }

        alert('Error processing files: ' + error.message);
    });
}

function getClothingProductSettings() {
    const clothingCard = document.querySelector('.clothing-products-card');
    if (!clothingCard) return { isActive: false };

    const isActive = clothingCard.querySelector('.toggle-btn').classList.contains('active');
    if (!isActive) return { isActive: false };

    return {
        isActive: true,
        shirts: {
            sidesOptions: clothingCard.querySelector('.shirts-control-group .dropdown-header span')?.textContent || 'Default (Front)'
        },
        pulloverHoodie: {
            sidesOptions: clothingCard.querySelector('.hoodie-control-group .dropdown-header span')?.textContent || 'Default (Front)'
        },
        zipHoodie: {
            sidesOptions: clothingCard.querySelector('.zip-control-group .dropdown-header span')?.textContent || 'Default (Front)'
        },
        allProducts: {
            colors: clothingCard.querySelector('.allproducts-colors-control-group .dropdown-header span')?.textContent || 'Skip',
            prices: clothingCard.querySelector('.allproducts-prices-control-group .dropdown-header span')?.textContent || 'Skip'
        }
    };
}

function getScalableProductSettings() {
    const scalableCard = document.querySelector('.scalable-products-card');
    if (!scalableCard) return { isActive: false };

    const isActive = scalableCard.querySelector('.toggle-btn').classList.contains('active');
    if (!isActive) return { isActive: false };

    const scaleOption = scalableCard.querySelector('.scale-options-control-group .dropdown-header span')?.textContent || 'Skip';
    let customScale = '';

    if (scaleOption === 'Custom scale') {
        const scaleInput = scalableCard.querySelector('.custom-scale-control-group .scale-input');
        if (scaleInput && !scaleInput.disabled) {
            customScale = scaleInput.value;
        }
    }

    const colorOption = scalableCard.querySelector('.color-options-control-group .dropdown-header span')?.textContent || 'Default (White)';
    let customColor = '';

    if (colorOption === 'Custom Color') {
        const colorInput = scalableCard.querySelector('.control-group-custom-colors-control-group .color-input');
        if (colorInput && !colorInput.disabled) {
            customColor = colorInput.value;
        }
    }

    return {
        isActive: true,
        scale: scaleOption,
        customScale: customScale,
        color: colorOption,
        customColor: customColor,
        prices: scalableCard.querySelector('.price-options-control-group .dropdown-header span')?.textContent || 'Skip'
    };
}

function getTumblerProductSettings() {
    const tumblerCard = document.querySelector('.tumbler-products-card');
    if (!tumblerCard) return { isActive: false };

    const isActive = tumblerCard.querySelector('.toggle-btn').classList.contains('active');
    if (!isActive) return { isActive: false };

    const scaleOption = tumblerCard.querySelector('.tumbler-scale-control-group .dropdown-header span')?.textContent || '100%';
    let customScale = '';

    if (scaleOption === 'Custom scale') {
        const scaleInput = tumblerCard.querySelector('.tumbler-custom-scale-control-group .scale-input');
        if (scaleInput && !scaleInput.disabled) {
            customScale = scaleInput.value;
        }
    }

    return {
        isActive: true,
        sides: tumblerCard.querySelector('.tumbler-sides-control-group .dropdown-header span')?.textContent || 'Default (One Side)',
        scale: scaleOption,
        customScale: customScale,
        colors: tumblerCard.querySelector('.tumbler-colors-control-group .dropdown-header span')?.textContent || 'Skip',
        prices: tumblerCard.querySelector('.tumbler-prices-control-group .dropdown-header span')?.textContent || 'Skip'
    };
}

function getMugProductSettings() {
    const mugCard = document.querySelector('.mug-products-card');
    if (!mugCard) return { isActive: false };

    const isActive = mugCard.querySelector('.toggle-btn').classList.contains('active');
    if (!isActive) return { isActive: false };

    const scaleOption = mugCard.querySelector('.mug-scale-control-group .dropdown-header span')?.textContent || '100%';
    let customScale = '';

    if (scaleOption === 'Custom scale') {
        const scaleInput = mugCard.querySelector('.mug-custom-scale-control-group .scale-input');
        if (scaleInput && !scaleInput.disabled) {
            customScale = scaleInput.value;
        }
    }

    return {
        isActive: true,
        sides: mugCard.querySelector('.mug-sides-control-group .dropdown-header span')?.textContent || 'Default (One Side)',
        scale: scaleOption,
        customScale: customScale,
        colors: mugCard.querySelector('.mug-colors-control-group .dropdown-header span')?.textContent || 'Original',
        prices: mugCard.querySelector('.mug-prices-control-group .dropdown-header span')?.textContent || 'Skip'
    };
}

    document.addEventListener('DOMContentLoaded', () => {
        const textSwapLink = document.querySelector('.text-swap-link');
        if (textSwapLink) {
            textSwapLink.addEventListener('click', (e) => {
                e.preventDefault();

                window.open('https://drive.google.com/file/d/1pGlS0MEfTy1mzgpIPufCV6iZ_NPHj-5z/view?usp=sharing', '_blank');
            });
        }
        
        setTimeout(initUploaderToggles, 500);
    });
    
    function initUploaderToggles() {
        const nativeUploaderRadio = document.getElementById('native-uploader');
        const snapUploaderRadio = document.getElementById('snap-uploader');
        if (!nativeUploaderRadio || !snapUploaderRadio) return;

        let useNativeUploader = localStorage.getItem('useNativeUploader') === 'true';
        
        nativeUploaderRadio.checked = useNativeUploader;
        snapUploaderRadio.checked = !useNativeUploader;
        
        nativeUploaderRadio.addEventListener('change', function() {
            localStorage.setItem('useNativeUploader', this.checked.toString());
            snapUploaderRadio.checked = !this.checked;
        });
        
        snapUploaderRadio.addEventListener('change', function() {
            localStorage.setItem('useNativeUploader', (!this.checked).toString());
            nativeUploaderRadio.checked = !this.checked;
        });
    }

    function handleScaleOptionChange(value, dropdown) {
        const customScaleInput = dropdown.closest('.scalable-products-controls')
            .querySelector('.custom-scale-control-group .custom-scale-input');

        if (value === 'Custom scale') {
            customScaleInput.classList.remove('disabled');
            const scaleInput = customScaleInput.querySelector('.scale-input');
            scaleInput.disabled = false;

            scaleInput.value = '50';

            setTimeout(() => scaleInput.focus(), 0);
        } else {

            const scaleInput = customScaleInput.querySelector('.scale-input');
            customScaleInput.classList.add('disabled');
            customScaleInput.classList.remove('error');
            scaleInput.classList.remove('error');
            scaleInput.disabled = true;
            scaleInput.value = '';

            customScaleInput.style.borderColor = '#DCE0E5';
            customScaleInput.querySelector('.percent-prefix').style.backgroundColor = '#DCE0E5';
            customScaleInput.querySelector('.percent-prefix span').style.color = '#606D85';
        }
    }

    function updateTumblerScaleOptions(value) {
        const customScaleInput = document.querySelector('.tumbler-products-card .tumbler-custom-scale-control-group .custom-scale-input');

        if (value === 'Custom scale') {
            customScaleInput.classList.remove('disabled');
            const scaleInput = customScaleInput.querySelector('.scale-input');
            scaleInput.disabled = false;

            scaleInput.value = '50';

            setTimeout(() => scaleInput.focus(), 0);
        } else {

            const scaleInput = customScaleInput.querySelector('.scale-input');
            customScaleInput.classList.add('disabled');
            customScaleInput.classList.remove('error');
            scaleInput.classList.remove('error');
            scaleInput.disabled = true;
            scaleInput.value = '';

            customScaleInput.style.borderColor = '#DCE0E5';
            customScaleInput.querySelector('.percent-prefix').style.backgroundColor = '#DCE0E5';
            customScaleInput.querySelector('.percent-prefix span').style.color = '#606D85';
        }
    }

    function resetToDefaults() {

        const checkboxes = {
            saveToDrafts: document.getElementById('save-to-drafts'),
            copyEnToAll: document.getElementById('copy-en-to-all'),
            autoTextSwap: document.getElementById('auto-text-swap'),
            termsCheckbox: document.getElementById('terms-checkbox')
        };

        Object.values(checkboxes).forEach(checkbox => {
            if (checkbox) {
                checkbox.checked = false;
                checkbox.style.filter = 'invert(97%) sepia(0%) saturate(0%) hue-rotate(246deg) brightness(103%) contrast(101%)';
            }
        });

        updateStartAutomationButton();


        resetDropdown('.clothing-products-card .shirts-control-group .snap-dropdown', 'Default (Front)');
        resetDropdown('.clothing-products-card .hoodie-control-group .snap-dropdown', 'Default (Front)');
        resetDropdown('.clothing-products-card .zip-control-group .snap-dropdown', 'Default (Front)');
        resetDropdown('.clothing-products-card .allproducts-colors-control-group .snap-dropdown', 'Skip');
        resetDropdown('.clothing-products-card .allproducts-prices-control-group .snap-dropdown', 'Skip');
        updateToggleState('.clothing-products-card', true);
        resetDropdown('.scalable-products-card .scale-options-control-group .snap-dropdown', 'Skip');
        resetDropdown('.scalable-products-card .color-options-control-group .snap-dropdown', 'Default (White)');
        resetDropdown('.scalable-products-card .price-options-control-group .snap-dropdown', 'Skip');
        updateToggleState('.scalable-products-card', true);

        const customScaleInput = document.querySelector('.control-group.custom-scale-control-group .custom-scale-input');
        if (customScaleInput) {
            const scaleInput = customScaleInput.querySelector('.scale-input');
            if (scaleInput) {
                scaleInput.value = '';
                scaleInput.disabled = true;
                scaleInput.classList.remove('error');
            }
            customScaleInput.classList.add('disabled');
            customScaleInput.classList.remove('error');
            customScaleInput.style.borderColor = '#DCE0E5';
            const percentPrefix = customScaleInput.querySelector('.percent-prefix');
            if (percentPrefix) {
                percentPrefix.style.backgroundColor = '#DCE0E5';
                percentPrefix.querySelector('span').style.color = '#606D85';
            }
        }

        const customColorInput = document.querySelector('.control-group-custom-colors-control-group .custom-color-input');
        if (customColorInput) {
            const hexInput = customColorInput.querySelector('.color-input');
            const colorIndicator = customColorInput.querySelector('.color-live-indicator');

            if (hexInput) {
                hexInput.value = '';
                hexInput.disabled = true;
                hexInput.classList.remove('error');
            }

            if (colorIndicator) {
                colorIndicator.style.backgroundColor = '#FFFFFF';
                colorIndicator.style.borderColor = '#C8CDD9';
            }

            customColorInput.classList.add('disabled');
            customColorInput.classList.remove('error');
            customColorInput.style.borderColor = '#DCE0E5';
            const hexPrefix = customColorInput.querySelector('.hex-prefix');
            if (hexPrefix) {
                hexPrefix.style.backgroundColor = '#DCE0E5';
                hexPrefix.querySelector('span').style.color = '#606D85';
            }
        }

        resetDropdown('.tumbler-products-card .tumbler-sides-control-group .snap-dropdown', 'Default (One Side)');
        resetDropdown('.tumbler-products-card .tumbler-scale-control-group .snap-dropdown', '100%');
        resetDropdown('.tumbler-products-card .tumbler-colors-control-group .snap-dropdown', 'Skip');
        resetDropdown('.tumbler-products-card .tumbler-prices-control-group .snap-dropdown', 'Skip');
        updateToggleState('.tumbler-products-card', true);

        resetDropdown('.mug-products-card .mug-sides-control-group .snap-dropdown', 'Default (One Side)');
        resetDropdown('.mug-products-card .mug-scale-control-group .snap-dropdown', '100%');
        resetDropdown('.mug-products-card .mug-colors-control-group .snap-dropdown', 'Original');
        resetDropdown('.mug-products-card .mug-prices-control-group .snap-dropdown', 'Skip');
        updateToggleState('.mug-products-card', true);

        const tumblerCustomScaleInput = document.querySelector('.tumbler-products-card .tumbler-custom-scale-control-group .custom-scale-input');
        if (tumblerCustomScaleInput) {
            const scaleInput = tumblerCustomScaleInput.querySelector('.scale-input');
            if (scaleInput) {
                scaleInput.value = '';
                scaleInput.disabled = true;
                scaleInput.classList.remove('error');
            }
            tumblerCustomScaleInput.classList.add('disabled');
            tumblerCustomScaleInput.classList.remove('error');
            tumblerCustomScaleInput.style.borderColor = '#DCE0E5';
            const percentPrefix = tumblerCustomScaleInput.querySelector('.percent-prefix');
            if (percentPrefix) {
                percentPrefix.style.backgroundColor = '#DCE0E5';
                percentPrefix.querySelector('span').style.color = '#606D85';
            }
        }
    }

    function resetDropdown(selector, defaultValue) {
        const dropdown = document.querySelector(selector);
        if (dropdown) {
            const header = dropdown.querySelector('.dropdown-header span');
            const items = dropdown.querySelectorAll('.dropdown-item');

            if (selector.includes('tumbler-sides-control-group') && defaultValue === 'Default (One Side)') {

                const scaleDropdown = document.querySelector('.tumbler-scale-control-group .snap-dropdown');
                if (scaleDropdown) {
                    const scaleDropdownList = scaleDropdown.querySelector('.dropdown-list');
                    if (scaleDropdownList) {

                        const patternItem = Array.from(scaleDropdownList.querySelectorAll('.dropdown-item')).find(item => 
                            item.textContent.includes('Pattern'));
                        if (patternItem) {
                            patternItem.remove();
                        }
                    }
                }
            }

            if (header) {
                header.textContent = defaultValue;

                if (selector.includes('color-options-control-group')) {
                    const headerContent = dropdown.querySelector('.dropdown-header .header-content');
                    if (headerContent) {

                        headerContent.innerHTML = '';

                        const colorIndicator = document.createElement('div');
                        colorIndicator.className = 'color-indicator';
                        colorIndicator.style.backgroundColor = '#FFFFFF';
                        headerContent.appendChild(colorIndicator);

                        const textSpan = document.createElement('span');
                        textSpan.textContent = defaultValue;
                        headerContent.appendChild(textSpan);
                    }
                }
            }

            items.forEach(item => {
                if (item.textContent.trim() === defaultValue) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
        } else {
        }
    }

    function updateToggleState(cardSelector, isActive) {
        const card = document.querySelector(cardSelector);
        if (!card) {
            return;
        }

        const toggleBtn = card.querySelector('.toggle-btn');
        const controls = card.querySelector('.clothing-products-controls, .scalable-products-controls, .tumbler-products-controls');

        if (toggleBtn && typeof isActive === 'boolean') {
            toggleBtn.classList.toggle('active', isActive);
            toggleBtn.style.backgroundColor = isActive ? '#470CED' : '#cfd4d4';

            if (!isActive) {
                card.classList.add('off');
                controls?.classList.add('off');
            } else {
                card.classList.remove('off');
                controls?.classList.remove('off');
            }
        }
    }

    function initResetButton() {
        const resetButton = document.querySelector('.reset-default-btn');
        if (resetButton) {
            resetButton.removeEventListener('click', resetToDefaults);
            resetButton.addEventListener('click', () => {
                resetToDefaults();

            });
        }
    }

    function closeWithoutSaving() {
        closePopup(false);

        loadPopupSettings();
    }

    function updateStartAutomationButton() {
        const startAutomationBtn = document.getElementById('start-automation-btn');
        const loadedFiles = document.getElementById('loadedFiles');
        const termsCheckbox = document.getElementById('terms-checkbox');

        const customScaleInputs = {
            scalable: {
                card: document.querySelector('.scalable-products-card'),
                input: document.querySelector('.scalable-products-card .custom-scale-input'),
                dropdown: document.querySelector('.scalable-products-card .scale-options-control-group .dropdown-header span')
            },
            tumbler: {
                card: document.querySelector('.tumbler-products-card'),
                input: document.querySelector('.tumbler-products-card .custom-scale-input'),
                dropdown: document.querySelector('.tumbler-products-card .tumbler-scale-control-group .dropdown-header span')
            },
            mug: {
                card: document.querySelector('.mug-products-card'),
                input: document.querySelector('.mug-products-card .custom-scale-input'),
                dropdown: document.querySelector('.mug-products-card .mug-scale-control-group .dropdown-header span')
            }
        };

        const colorInputValidation = {
            card: document.querySelector('.scalable-products-card'),
            input: document.querySelector('.scalable-products-card .control-group-custom-colors-control-group .custom-color-input'),
            dropdown: document.querySelector('.scalable-products-card .color-options-control-group .dropdown-header .header-content span')
        };

        if (startAutomationBtn) {
            let shouldDisable = false;

            if (!loadedFiles || !loadedFiles.classList.contains('active') || !termsCheckbox?.checked) {
                shouldDisable = true;
            }

            Object.values(customScaleInputs).forEach(({ card, input, dropdown }) => {
                if (card && 
                    !card.classList.contains('off') && 
                    input && 
                    dropdown && 
                    dropdown.textContent.trim() === 'Custom scale') {

                    if (!input.classList.contains('disabled')) {
                        const scaleInput = input.querySelector('.scale-input');
                        if (!scaleInput || 
                            !scaleInput.value || 
                            input.classList.contains('error') || 
                            parseInt(scaleInput.value) < 50 || 
                            parseInt(scaleInput.value) > 100) {
                            shouldDisable = true;
                        }
                    }
                }
            });

            if (colorInputValidation.card && 
                !colorInputValidation.card.classList.contains('off') && 
                colorInputValidation.input && 
                colorInputValidation.dropdown && 
                colorInputValidation.dropdown.textContent.trim() === 'Custom Color') {

                if (!colorInputValidation.input.classList.contains('disabled')) {
                    const hexInput = colorInputValidation.input.querySelector('.color-input');
                    if (!hexInput || 
                        !hexInput.value || 
                        colorInputValidation.input.classList.contains('error') || 
                        hexInput.value.length !== 6) {
                        shouldDisable = true;
                    }
                }
            }

            if (shouldDisable) {
                startAutomationBtn.classList.add('disabled');
                startAutomationBtn.disabled = true;
            } else {
                startAutomationBtn.classList.remove('disabled');
                startAutomationBtn.disabled = false;
            }
        }
    }

    function handleColorOptionChange(value, dropdown) {

        const customColorInput = document.querySelector('.scalable-products-card .control-group-custom-colors-control-group .custom-color-input');
        const hexInput = customColorInput?.querySelector('.color-input');


        if (value === 'Custom Color') {

            if (customColorInput && hexInput) {
                customColorInput.classList.remove('disabled');
                hexInput.disabled = false;

                hexInput.value = 'FFFFFF';

                const colorIndicator = customColorInput.querySelector('.color-live-indicator');
                if (colorIndicator) {
                    colorIndicator.style.backgroundColor = '#FFFFFF';
                }

                customColorInput.style.borderColor = '#DCE0E5';
                const hexPrefix = customColorInput.querySelector('.hex-prefix');
                if (hexPrefix) {
                    hexPrefix.style.backgroundColor = '#DCE0E5';
                    hexPrefix.querySelector('span').style.color = '#606D85';
                }

                setTimeout(() => hexInput.focus(), 0);

            }
        } else {

            if (customColorInput && hexInput) {
                customColorInput.classList.add('disabled');
                customColorInput.classList.remove('error');
                hexInput.classList.remove('error');
                hexInput.disabled = true;
                hexInput.value = '';

                customColorInput.style.borderColor = '#DCE0E5';
                const hexPrefix = customColorInput.querySelector('.hex-prefix');
                if (hexPrefix) {
                    hexPrefix.style.backgroundColor = '#DCE0E5';
                    hexPrefix.querySelector('span').style.color = '#606D85';
                }

                const colorIndicator = customColorInput.querySelector('.color-live-indicator');
                if (colorIndicator) {

                    colorIndicator.style.cssText = `
                        width: 16px;
                        height: 16px;
                        border-radius: 50%;
                        border: 0.6px solid #C8CDD9;
                        margin: 0 8px;
                        background-color: #FFFFFF !important;
                        flex-shrink: 0;
                    `;
                }

            }
        }
    }

    function initCustomColorInput() {
        const customColorInput = document.querySelector('.control-group-custom-colors-control-group .custom-color-input');
        const hexInput = customColorInput ? customColorInput.querySelector('.color-input') : null;
        if (hexInput) {

            hexInput.addEventListener('input', () => {
                updateStartAutomationButton();
            });
            hexInput.addEventListener('blur', () => {
                updateStartAutomationButton();
            });
        }
    }

    initCustomColorInput();

    function toggleDonationButton(show = true) {
        const donationBtn = document.querySelector('.donation-btn');
        if (donationBtn) {
            if (show) {
                donationBtn.classList.add('show');
            } else {
                donationBtn.classList.remove('show');
            }
        }
    }

    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('donation-btn')) {
            window.open('https://buymeacoffee.com/nemocreativestudio', '_blank');
        }
    });

    function initDonationButton() {
        const donationBtn = document.querySelector('.donation-btn-wrapper');
        if (donationBtn) {

            const newBtn = donationBtn.cloneNode(true);
            donationBtn.parentNode.replaceChild(newBtn, donationBtn);

            newBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                window.open('https://buymeacoffee.com/nemocreativestudio', '_blank');
            });
        }
    }

    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.addedNodes.length) {
                const donationBtn = document.querySelector('.donation-btn');
                if (donationBtn) {
                    initDonationButton();
                    observer.disconnect();
                }
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    function initColorDropdown() {
        const colorDropdown = document.querySelector('.color-options-control-group .snap-dropdown');
        if (!colorDropdown) {
            return;
        }

        const header = colorDropdown.querySelector('.dropdown-header');
        const menu = colorDropdown.querySelector('.dropdown-menu');

        const clonedItems = [];

        colorDropdown.querySelectorAll('.dropdown-item').forEach(item => {

            const clone = item.cloneNode(true);
            item.parentNode.replaceChild(clone, item);

            clonedItems.push(clone);

            clone.addEventListener('click', (e) => {
                e.stopPropagation();

                if(clone.classList.contains('selected')) {
                    return;
                }

                const selectedColorIndicator = clone.querySelector('.color-indicator');
                const value = clone.textContent.trim();
                

                const headerContent = header.querySelector('.header-content');
                const headerColorIndicator = headerContent.querySelector('.color-indicator');

                if (value === 'Custom Color') {
                    headerColorIndicator.innerHTML = '<img src="' + chrome.runtime.getURL('assets/colorwheel-ic.svg') + '" alt="Color Wheel">';
                    headerColorIndicator.style.backgroundColor = '';
                } else {
                    headerColorIndicator.innerHTML = '';
                    headerColorIndicator.style.backgroundColor = selectedColorIndicator.style.backgroundColor;
                }

                headerContent.querySelector('span').textContent = value;

                clonedItems.forEach(i => i.classList.remove('selected'));
                clone.classList.add('selected');

                menu.classList.remove('show');
                colorDropdown.classList.remove('focused');

                const customColorInput = document.querySelector('.control-group-custom-colors-control-group .custom-color-input');
                const hexInput = customColorInput?.querySelector('.color-input');


                if (value === 'Custom Color') {

                    if (customColorInput && hexInput) {
                        customColorInput.classList.remove('disabled');
                        hexInput.disabled = false;

                        const colorIndicator = customColorInput.querySelector('.color-live-indicator');
                        if (colorIndicator) {
                            colorIndicator.style.backgroundColor = '#FFFFFF';
                        }

                        if (!hexInput.value) {
                            hexInput.value = 'FFFFFF';
                        } else {

                            hexInput.value = 'FFFFFF';
                        }

                        updateLiveColorIndicator(customColorInput, 'FFFFFF');

                        customColorInput.style.borderColor = '#DCE0E5';
                        const hexPrefix = customColorInput.querySelector('.hex-prefix');
                        if (hexPrefix) {
                            hexPrefix.style.backgroundColor = '#DCE0E5';
                            hexPrefix.querySelector('span').style.color = '#606D85';
                        }

                        setTimeout(() => hexInput.focus(), 0);

                    }
                } else {

                    if (customColorInput && hexInput) {
                        customColorInput.classList.add('disabled');
                        customColorInput.classList.remove('error');
                        hexInput.classList.remove('error');
                        hexInput.disabled = true;
                        hexInput.value = '';

                        customColorInput.style.borderColor = '#DCE0E5';
                        const hexPrefix = customColorInput.querySelector('.hex-prefix');
                        if (hexPrefix) {
                            hexPrefix.style.backgroundColor = '#DCE0E5';
                            hexPrefix.querySelector('span').style.color = '#606D85';
                        }

                        const colorIndicator = customColorInput.querySelector('.color-live-indicator');
                        if (colorIndicator) {

                            colorIndicator.style.cssText = `
                                width: 16px;
                                height: 16px;
                                border-radius: 50%;
                                border: 0.6px solid #C8CDD9;
                                margin: 0 8px;
                                background-color: #FFFFFF !important;
                                flex-shrink: 0;
                            `;
                        }

                    }
                }

                updateStartAutomationButton();
            });
        });

    }

    function initScrollControl() {
        const container = document.querySelector('.snap-bulk-upload-container');
        if (container) {

            container.addEventListener('wheel', (e) => {

                const isFromDropdownList = e.target.closest('.dropdown-list');

                if (isFromDropdownList) {

                    return;
                }

                const isAtTop = container.scrollTop === 0;
                const isAtBottom = container.scrollHeight - container.scrollTop === container.clientHeight;

                if ((isAtTop && e.deltaY < 0) || (isAtBottom && e.deltaY > 0)) {
                    e.preventDefault();
                }

                e.stopPropagation();
            }, { passive: false });

            let startY = 0;

            container.addEventListener('touchstart', (e) => {
                startY = e.touches[0].clientY;
            }, { passive: false });

            container.addEventListener('touchmove', (e) => {

                const isFromDropdownList = e.target.closest('.dropdown-list');

                if (isFromDropdownList) {
                    return;
                }

                const currentY = e.touches[0].clientY;
                const isScrollingUp = currentY > startY;
                const isScrollingDown = currentY < startY;

                const isAtTop = container.scrollTop <= 0;
                const isAtBottom = Math.abs(container.scrollHeight - container.scrollTop - container.clientHeight) < 1;

                if ((isAtTop && isScrollingUp) || (isAtBottom && isScrollingDown)) {
                    e.preventDefault();
                }

                startY = currentY;
            }, { passive: false });
        }
    }

    initScrollControl();

    function initCustomColorInputValidation() {

        injectColorIndicator();

        document.addEventListener('input', function(e) {

            if (e.target.classList.contains('color-input')) {
                const container = e.target.closest('.custom-color-input');
                if (!container) return;

                let value = e.target.value.replace(/[^0-9A-Fa-f]/g, '').toUpperCase();
                value = value.slice(0, 6);

                e.target.value = value;

                updateLiveColorIndicator(container, value);

                const hasError = value.length !== 6;

                updateCustomColorError(container, hasError);

                updateStartAutomationButton();
            }
        });

        document.addEventListener('blur', function(e) {
            if (e.target.classList.contains('color-input')) {
                const container = e.target.closest('.custom-color-input');
                if (!container) return;

                const value = e.target.value;
                const hasError = value.length !== 6;

                updateCustomColorError(container, hasError);
                updateLiveColorIndicator(container, value);

                updateStartAutomationButton();
            }
        }, true);

        document.addEventListener('paste', function(e) {
            if (e.target.classList.contains('color-input')) {
                e.preventDefault();

                const pastedText = (e.clipboardData || window.clipboardData).getData('text');
                const cleanedText = pastedText.replace(/[^0-9A-Fa-f]/g, '').toUpperCase().slice(0, 7);

                const input = e.target;
                const startPos = input.selectionStart;
                const endPos = input.selectionEnd;
                const beforeText = input.value.substring(0, startPos);
                const afterText = input.value.substring(endPos);

                const newValue = (beforeText + cleanedText + afterText).slice(0, 7);
                input.value = newValue;

                const newCursorPos = Math.min(startPos + cleanedText.length, 7);
                input.setSelectionRange(newCursorPos, newCursorPos);

                const container = input.closest('.custom-color-input');
                updateCustomColorError(container, newValue.length < 6 || newValue.length > 6);

                updateStartAutomationButton();
            }
        });
    }

    function updateCustomColorError(container, hasError) {
        if (hasError) {
            container.classList.add('error');
            container.style.borderColor = '#FF391F';
            const hexPrefix = container.querySelector('.hex-prefix');
            if (hexPrefix) {
                hexPrefix.style.backgroundColor = '#FF391F';
                hexPrefix.querySelector('span').style.color = '#FFFFFF';
            }
            container.querySelector('.color-input').classList.add('error');
        } else {
            container.classList.remove('error');
            container.style.borderColor = '#DCE0E5';
            const hexPrefix = container.querySelector('.hex-prefix');
            if (hexPrefix) {
                hexPrefix.style.backgroundColor = '#DCE0E5';
                hexPrefix.querySelector('span').style.color = '#606D85';
            }
            container.querySelector('.color-input').classList.remove('error');
        }
    }

    function updateScalableColor(colorOption, customValue) {
        const colorDropdown = document.querySelector('.scalable-products-card .color-options-control-group .snap-dropdown');
        const customColorInput = document.querySelector('.scalable-products-card .control-group-custom-colors-control-group .custom-color-input');
        const hexInput = customColorInput?.querySelector('.color-input');

        if (colorDropdown && customColorInput && hexInput) {
            if (colorOption === 'Custom Color') {
                customColorInput.classList.remove('disabled');
                hexInput.disabled = false;

                let cleanValue = (customValue || '').replace(/[^0-9A-Fa-f]/g, '').toUpperCase();

                cleanValue = cleanValue.slice(0, 7);
                if (cleanValue.length !== 6) {
                    cleanValue = 'FFFFFF'; 
                }
                hexInput.value = cleanValue;

                updateCustomColorError(customColorInput, cleanValue.length < 6 || cleanValue.length > 6);

                const headerContent = colorDropdown.querySelector('.dropdown-header .header-content');
                if (headerContent) {
                    const colorIndicator = headerContent.querySelector('.color-indicator');
                    if (colorIndicator) {
                        colorIndicator.innerHTML = '<img src="' + chrome.runtime.getURL('assets/colorwheel-ic.svg') + '" alt="Color Wheel">';
                        colorIndicator.style.backgroundColor = '';
                    }
                }
            } else {
                customColorInput.classList.add('disabled');
                hexInput.disabled = true;
                hexInput.value = '';
                customColorInput.classList.remove('error');
                hexInput.classList.remove('error');
            }
        }
    }

    function loadPopupSettings() {

        initCustomColorInputValidation();
    }

    function injectColorIndicator() {

        const customColorInput = document.querySelector('.scalable-products-card .control-group-custom-colors-control-group .custom-color-input');
        if (!customColorInput || customColorInput.querySelector('.color-live-indicator')) return;

        const colorIndicator = document.createElement('div');
        colorIndicator.className = 'color-live-indicator';
        colorIndicator.style.cssText = `
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 0.6px solid #C8CDD9;
            margin: 0 8px;
            background-color: #FFFFFF;
            flex-shrink: 0;
        `;

        const hexPrefix = customColorInput.querySelector('.hex-prefix');
        if (hexPrefix) {
            hexPrefix.insertAdjacentElement('afterend', colorIndicator);
        }
    }

    function updateLiveColorIndicator(container, value) {
        const colorIndicator = container.querySelector('.color-live-indicator');
        if (!colorIndicator) return;

        if (value.length === 6) {

            try {
                colorIndicator.style.backgroundColor = '#' + value;
            } catch (e) {

                colorIndicator.style.backgroundColor = '#FFFFFF';
            }
        } else {

            colorIndicator.style.backgroundColor = '#FFFFFF';
        }
    }

    function initCustomColorInputValidation() {

        injectColorIndicator();

        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('color-input')) {
                const container = e.target.closest('.custom-color-input');
                if (!container) return;

                let value = e.target.value.replace(/[^0-9A-Fa-f]/g, '').toUpperCase();
                value = value.slice(0, 6);

                e.target.value = value;

                updateLiveColorIndicator(container, value);

                const hasError = value.length !== 6; 
                updateCustomColorError(container, hasError);
                updateStartAutomationButton();
            }
        });

        document.addEventListener('blur', function(e) {
            if (e.target.classList.contains('color-input')) {
                const container = e.target.closest('.custom-color-input');
                if (!container) return;

                const value = e.target.value;
                const hasError = value.length !== 6; 

                updateCustomColorError(container, hasError);
                updateLiveColorIndicator(container, value);
                updateStartAutomationButton();
            }
        }, true);
    }

    function updateScalableColor(colorOption, customValue) {
        const colorDropdown = document.querySelector('.scalable-products-card .color-options-control-group .snap-dropdown');
        const customColorInput = document.querySelector('.scalable-products-card .control-group-custom-colors-control-group .custom-color-input');
        const hexInput = customColorInput?.querySelector('.color-input');

        if (colorDropdown && customColorInput && hexInput) {
            if (colorOption === 'Custom Color') {

                if (!customColorInput.querySelector('.color-live-indicator')) {
                    injectColorIndicator();
                }

                customColorInput.classList.remove('disabled');
                hexInput.disabled = false;

                let cleanValue = (customValue || '').replace(/[^0-9A-Fa-f]/g, '').toUpperCase();
                cleanValue = cleanValue.slice(0, 6);
                if (cleanValue.length !== 6) {
                    cleanValue = 'FFFFFF';
                }
                hexInput.value = cleanValue;

                updateLiveColorIndicator(customColorInput, cleanValue);

                updateCustomColorError(customColorInput, cleanValue.length !== 6);

                const headerContent = colorDropdown.querySelector('.dropdown-header .header-content');
                if (headerContent) {
                    const colorIndicator = headerContent.querySelector('.color-indicator');
                    if (colorIndicator) {
                        colorIndicator.innerHTML = '<img src="' + chrome.runtime.getURL('assets/colorwheel-ic.svg') + '" alt="Color Wheel">';
                        colorIndicator.style.backgroundColor = '';
                    }
                }
            } else {
                customColorInput.classList.add('disabled');
                hexInput.disabled = true;
                hexInput.value = '';
                customColorInput.classList.remove('error');
                hexInput.classList.remove('error');

                const colorIndicator = customColorInput.querySelector('.color-live-indicator');
                if (colorIndicator) {
                    colorIndicator.style.backgroundColor = '#FFFFFF';
                }
            }
        }
    }

    function handleColorOptionChange(value, dropdown) {
        if (value === 'Custom Color') {
            const customColorInput = document.querySelector('.scalable-products-card .control-group-custom-colors-control-group .custom-color-input');
            if (customColorInput && !customColorInput.querySelector('.color-live-indicator')) {
                injectColorIndicator();

                if (!document.querySelector('#custom-color-input-styles')) {
                    const customStyleEl = document.createElement("style");
                    customStyleEl.id = 'custom-color-input-styles';
                    customStyleEl.textContent = `

                        .custom-color-input {
                            display: flex;
                            align-items: center;
                            border: 1.5px solid #DCE0E5;
                            border-radius: 4px;
                            padding: 0;
                            height: 40px;
                            width: 100%;
                            background: #FFFFFF;
                            gap: 0;
                        }

                        .custom-color-input .hex-prefix {
                            width: 32px !important;
                    `;
                    document.head.appendChild(customStyleEl);
                }
            }
        }
    }

    function updateLiveColorIndicator(container, value) {
        const colorIndicator = container.querySelector('.color-live-indicator');
        if (!colorIndicator) return;

        if (value.length === 6) {

            try {
                colorIndicator.style.backgroundColor = '#' + value;
            } catch (e) {

                colorIndicator.style.backgroundColor = '#FFFFFF';
            }
        } else {

            colorIndicator.style.backgroundColor = '#FFFFFF';
        }
    }

    function updateCustomColorError(container, hasError) {
        if (hasError) {
            container.classList.add('error');
            container.style.borderColor = '#FF391F';
            const hexPrefix = container.querySelector('.hex-prefix');
            if (hexPrefix) {
                hexPrefix.style.backgroundColor = '#FF391F';
                hexPrefix.querySelector('span').style.color = '#FFFFFF';
            }
            container.querySelector('.color-input').classList.add('error');
        } else {
            container.classList.remove('error');
            container.style.borderColor = '#DCE0E5';
            const hexPrefix = container.querySelector('.hex-prefix');
            if (hexPrefix) {
                hexPrefix.style.backgroundColor = '#DCE0E5';
                hexPrefix.querySelector('span').style.color = '#606D85';
            }
            container.querySelector('.color-input').classList.remove('error');
        }
    }

    styleEl.textContent = styleEl.textContent.replace(
        /\.custom-color-input \.color-live-indicator \{[^}]+\}/,
        '.custom-color-input .color-live-indicator {' +
            'width: 16px;' +
            'height: 16px;' +
            'border-radius: 50%;' +
            'border: 0.6px solid #C8CDD9 !important;' +
            'margin-left: 8px;' +
            'margin-right: 4px;' +
            'background-color: #FFFFFF;' +
            'flex-shrink: 0;' +
            'transition: background-color 0.2s ease;' +
        '}'
    );

    styleEl.textContent = styleEl.textContent.replace(
        /\.custom-color-input\.error \.color-live-indicator \{[^}]+\}/,
        '.custom-color-input.error .color-live-indicator {' +
            'border-color: #C8CDD9 !important;' +
        '}'
    );

    styleEl.textContent = styleEl.textContent.replace(
        /\.custom-color-input\.disabled \.color-live-indicator \{[^}]+\}/,
        '.custom-color-input.disabled .color-live-indicator {' +
            'opacity: 0.5;' +
            'background-color: #FFFFFF !important;' +
            'border-color: #C8CDD9 !important;' +
        '}'
    );

    function SaveSnapSettings() {
        const settings = {
            saveToDrafts: document.getElementById('save-to-drafts')?.checked || false,
            copyEnToAll: document.getElementById('copy-en-to-all')?.checked || false,
            autoTextSwap: document.getElementById('auto-text-swap')?.checked || false,
            clothingProducts: {},
            scalableProducts: {},
            tumblerProducts: {}
        };

        const clothingProductCards = document.querySelectorAll('.clothing-products-card');
        clothingProductCards.forEach(card => {
            const headerText = card.querySelector('.header-text').textContent;
            const toggleBtn = card.querySelector('.toggle-btn');

            const shirtsDropdown = card.querySelector('.shirts-control-group .snap-dropdown .dropdown-header span');
            const pulloverHoodieDropdown = card.querySelector('.hoodie-control-group .snap-dropdown .dropdown-header span');
            const zipHoodieDropdown = card.querySelector('.zip-control-group .snap-dropdown .dropdown-header span');

            const colorsDropdown = card.querySelector('.allproducts-colors-control-group .snap-dropdown .dropdown-header span');
            const pricesDropdown = card.querySelector('.allproducts-prices-control-group .snap-dropdown .dropdown-header span');

            settings.clothingProducts[headerText] = {
                isActive: toggleBtn.classList.contains('active'),
                shirts: {
                    sides: shirtsDropdown ? shirtsDropdown.textContent : 'Default (Front)'
                },
                pulloverHoodie: {
                    sides: pulloverHoodieDropdown ? pulloverHoodieDropdown.textContent : 'Default (Front)'
                },
                zipHoodie: {
                    sides: zipHoodieDropdown ? zipHoodieDropdown.textContent : 'Default (Front)'
                },
                colors: colorsDropdown ? colorsDropdown.textContent : 'Skip',
                prices: pricesDropdown ? pricesDropdown.textContent : 'Skip'
            };
        });

        const scalableProductCards = document.querySelectorAll('.scalable-products-card');
        scalableProductCards.forEach(card => {
            const headerText = card.querySelector('.header-text').textContent;
            const toggleBtn = card.querySelector('.toggle-btn');
            const scaleDropdown = card.querySelector('.scale-options-control-group .snap-dropdown .dropdown-header span');
            const colorDropdown = card.querySelector('.color-options-control-group .snap-dropdown .dropdown-header span');
            const priceDropdown = card.querySelector('.price-options-control-group .snap-dropdown .dropdown-header span');

            let customScaleValue = "";
            if (scaleDropdown && scaleDropdown.textContent.trim() === "Custom scale") {
                const customScaleInput = card.querySelector('.custom-scale-control-group .scale-input');

                if (customScaleInput && !customScaleInput.classList.contains('error') && !customScaleInput.closest('.custom-scale-input')?.classList.contains('error')) {
                    customScaleValue = customScaleInput.value;
                }
            }

            let customColorValue = "";
            if (colorDropdown && colorDropdown.textContent.trim() === "Custom Color") {
                const customColorInput = card.querySelector('.control-group-custom-colors-control-group .custom-color-input .color-input');

                if (customColorInput && !customColorInput.classList.contains('error') && !customColorInput.closest('.custom-color-input')?.classList.contains('error')) {
                    customColorValue = customColorInput.value;
                }
            }

            settings.scalableProducts[headerText] = {
                isActive: toggleBtn.classList.contains('active'),
                scale: scaleDropdown ? scaleDropdown.textContent : '',
                customScale: customScaleValue,
                color: colorDropdown ? colorDropdown.textContent : '',
                customColor: customColorValue,
                price: priceDropdown ? priceDropdown.textContent : ''
            };
        });

        const tumblerProductCards = document.querySelectorAll('.tumbler-products-card');
        tumblerProductCards.forEach(card => {
            const headerText = card.querySelector('.header-text').textContent;
            const toggleBtn = card.querySelector('.toggle-btn');
            const sidesDropdown = card.querySelector('.tumbler-sides-control-group .snap-dropdown .dropdown-header span');
            const scaleDropdown = card.querySelector('.tumbler-scale-control-group .snap-dropdown .dropdown-header span');
            const colorDropdown = card.querySelector('.tumbler-colors-control-group .snap-dropdown .dropdown-header span');
            const priceDropdown = card.querySelector('.tumbler-prices-control-group .snap-dropdown .dropdown-header span');

            let customScaleValue = "";
            if (scaleDropdown && scaleDropdown.textContent.trim() === "Custom scale") {
                const customScaleInput = card.querySelector('.tumbler-custom-scale-control-group .scale-input');

                if (customScaleInput && !customScaleInput.classList.contains('error') && !customScaleInput.closest('.custom-scale-input')?.classList.contains('error')) {
                    customScaleValue = customScaleInput.value;
                }
            }

            settings.tumblerProducts[headerText] = {
                isActive: toggleBtn.classList.contains('active'),
                sides: sidesDropdown ? sidesDropdown.textContent : '',
                scale: scaleDropdown ? scaleDropdown.textContent : '100%',
                customScale: customScaleValue,
                color: colorDropdown ? colorDropdown.textContent : '',
                price: priceDropdown ? priceDropdown.textContent : ''
            };
        });

        const mugProductCards = document.querySelectorAll('.mug-products-card');
        mugProductCards.forEach(card => {
            const headerText = card.querySelector('.header-text').textContent;
            const toggleBtn = card.querySelector('.toggle-btn');
            const sidesDropdown = card.querySelector('.mug-sides-control-group .snap-dropdown .dropdown-header span');
            const scaleDropdown = card.querySelector('.mug-scale-control-group .snap-dropdown .dropdown-header span');
            const colorDropdown = card.querySelector('.mug-colors-control-group .snap-dropdown .dropdown-header span');
            const priceDropdown = card.querySelector('.mug-prices-control-group .snap-dropdown .dropdown-header span');

            let customScaleValue = "";
            if (scaleDropdown && scaleDropdown.textContent.trim() === "Custom scale") {
                const customScaleInput = card.querySelector('.mug-custom-scale-control-group .scale-input');

                if (customScaleInput && !customScaleInput.classList.contains('error') && !customScaleInput.closest('.custom-scale-input')?.classList.contains('error')) {
                    customScaleValue = customScaleInput.value;
                }
            }

            settings.tumblerProducts[headerText] = {
                isActive: toggleBtn.classList.contains('active'),
                sides: sidesDropdown ? sidesDropdown.textContent : '',
                scale: scaleDropdown ? scaleDropdown.textContent : '100%',
                customScale: customScaleValue,
                color: colorDropdown ? colorDropdown.textContent : '',
                price: priceDropdown ? priceDropdown.textContent : ''
            };
        });

        const nativeUploaderRadio = document.getElementById('native-uploader');
        if (nativeUploaderRadio) {
            settings.uploaderType = nativeUploaderRadio.checked ? 'native' : 'snap';
        }
        localStorage.setItem('snapSettings', JSON.stringify(settings));
    }

    function loadSnapSettings() {
        const saved = localStorage.getItem('snapSettings');
        if (!saved) { 
            return;
        }
        const settings = JSON.parse(saved);

        const saveToDraftsCheckbox = document.getElementById('save-to-drafts');
        const copyEnToAllCheckbox = document.getElementById('copy-en-to-all');
        const autoTextSwapCheckbox = document.getElementById('auto-text-swap');
        const termsCheckbox = document.getElementById('terms-checkbox');

        if (saveToDraftsCheckbox) {
            saveToDraftsCheckbox.checked = settings.saveToDrafts;
            saveToDraftsCheckbox.style.filter = settings.saveToDrafts ? 'none' : 'invert(97%) sepia(0%) saturate(0%) hue-rotate(246deg) brightness(103%) contrast(101%)';
        }

        if (copyEnToAllCheckbox) {
            copyEnToAllCheckbox.checked = settings.copyEnToAll;
            copyEnToAllCheckbox.style.filter = settings.copyEnToAll ? 'none' : 'invert(97%) sepia(0%) saturate(0%) hue-rotate(246deg) brightness(103%) contrast(101%)';
        }

        if (autoTextSwapCheckbox) {
            autoTextSwapCheckbox.checked = settings.autoTextSwap;
            autoTextSwapCheckbox.style.filter = settings.autoTextSwap ? 'none' : 'invert(97%) sepia(0%) saturate(0%) hue-rotate(246deg) brightness(103%) contrast(101%)';
        }

        if (termsCheckbox) {
            termsCheckbox.checked = false;
            termsCheckbox.style.filter = 'invert(97%) sepia(0%) saturate(0%) hue-rotate(246deg) brightness(103%) contrast(101%)';
        }

        const clothingCards = document.querySelectorAll('.clothing-products-card');
        clothingCards.forEach(card => {
            const headerEl = card.querySelector('.header-text');
            if (!headerEl) return;
            const headerText = headerEl.textContent;
            const data = settings.clothingProducts[headerText];
            if (data) {

                const toggleBtn = card.querySelector('.toggle-btn');
                const controls = card.querySelector('.clothing-products-controls');
                if (data.isActive) {
                    toggleBtn.classList.add('active');
                    toggleBtn.style.backgroundColor = '#470CED';
                    card.classList.remove('off');
                    if (controls) controls.classList.remove('off');
                } else {
                    toggleBtn.classList.remove('active');
                    toggleBtn.style.backgroundColor = '#cfd4d4';
                    card.classList.add('off');
                    if (controls) controls.classList.add('off');
                }

                const shirtsDropdown = card.querySelector('.shirts-control-group .snap-dropdown');
                if (data.shirts && data.shirts.sides) {
                    simulateDropdownSelection(shirtsDropdown, data.shirts.sides);
                }

                const pulloverHoodieDropdown = card.querySelector('.hoodie-control-group .snap-dropdown');
                if (data.pulloverHoodie && data.pulloverHoodie.sides) {
                    simulateDropdownSelection(pulloverHoodieDropdown, data.pulloverHoodie.sides);
                }

                const zipHoodieDropdown = card.querySelector('.zip-control-group .snap-dropdown');
                if (data.zipHoodie && data.zipHoodie.sides) {
                    simulateDropdownSelection(zipHoodieDropdown, data.zipHoodie.sides);
                }

                const colorsDropdown = card.querySelector('.allproducts-colors-control-group .snap-dropdown');
                simulateDropdownSelection(colorsDropdown, data.colors);

                const pricesDropdown = card.querySelector('.allproducts-prices-control-group .snap-dropdown');
                simulateDropdownSelection(pricesDropdown, data.prices);
            }
        });

        const scalableCards = document.querySelectorAll('.scalable-products-card');
        scalableCards.forEach(card => {
            const headerEl = card.querySelector('.header-text');
            if (!headerEl) return;
            const headerText = headerEl.textContent;
            const data = settings.scalableProducts[headerText];
            if (data) {

                const toggleBtn = card.querySelector('.toggle-btn');
                const controls = card.querySelector('.scalable-products-controls');
                if (data.isActive) {
                    toggleBtn.classList.add('active');
                    toggleBtn.style.backgroundColor = '#470CED';
                    card.classList.remove('off');
                    if (controls) controls.classList.remove('off');
                } else {
                    toggleBtn.classList.remove('active');
                    toggleBtn.style.backgroundColor = '#cfd4d4';
                    card.classList.add('off');
                    if (controls) controls.classList.add('off');
                }

                if (data.color === 'Custom Color') {
                    const customColorInput = card.querySelector('.control-group-custom-colors-control-group .custom-color-input');
                    const hexInput = customColorInput?.querySelector('.color-input');

                    if (customColorInput && hexInput) {

                        customColorInput.classList.remove('disabled');
                        hexInput.disabled = false;

                        if (data.customColor) {

                            hexInput.value = '';

                            hexInput.focus();

                            const value = data.customColor;
                            for (let i = 0; i < value.length; i++) {
                                hexInput.value += value[i];
                                hexInput.dispatchEvent(new Event('input', { bubbles: true }));
                            }

                            hexInput.blur();
                        } else {

                            hexInput.focus();
                            'FFFFFF'.split('').forEach(char => {
                                hexInput.value += char;
                                hexInput.dispatchEvent(new Event('input', { bubbles: true }));
                            });
                            hexInput.blur();
                        }
                    }
                }

                const scaleDropdown = card.querySelector('.scale-options-control-group .snap-dropdown');
                simulateDropdownSelection(scaleDropdown, data.scale);

                if (data.scale === 'Custom scale' && data.customScale) {
                    const customScaleInput = card.querySelector('.custom-scale-control-group .scale-input');
                    if (customScaleInput) {
                        customScaleInput.value = data.customScale;
                        customScaleInput.dispatchEvent(new Event('input'));
                    }
                }

                const colorDropdown = card.querySelector('.color-options-control-group .snap-dropdown');
                simulateDropdownSelection(colorDropdown, data.color);

                const priceDropdown = card.querySelector('.price-options-control-group .snap-dropdown');
                simulateDropdownSelection(priceDropdown, data.price);
            }
        });

        const tumblerCards = document.querySelectorAll('.tumbler-products-card');
        tumblerCards.forEach(card => {
            const headerEl = card.querySelector('.header-text');
            if (!headerEl) return;
            const headerText = headerEl.textContent;
            const data = settings.tumblerProducts[headerText];
            if (data) {

                const toggleBtn = card.querySelector('.toggle-btn');
                const controls = card.querySelector('.tumbler-products-controls');
                if (data.isActive) {
                    toggleBtn.classList.add('active');
                    toggleBtn.style.backgroundColor = '#470CED';
                    card.classList.remove('off');
                    if (controls) controls.classList.remove('off');
                } else {
                    toggleBtn.classList.remove('active');
                    toggleBtn.style.backgroundColor = '#cfd4d4';
                    card.classList.add('off');
                    if (controls) controls.classList.add('off');
                }

                const sidesDropdown = card.querySelector('.tumbler-sides-control-group .snap-dropdown');
                simulateDropdownSelection(sidesDropdown, data.sides);

                if (data.sides === 'Two Sides') {
                    const scaleDropdown = card.querySelector('.tumbler-scale-control-group .snap-dropdown');
                    const scaleDropdownList = scaleDropdown?.querySelector('.dropdown-list');

                    if (scaleDropdownList && !scaleDropdownList.querySelector('.dropdown-item:last-child')?.textContent.includes('Pattern')) {

                        const patternItem = document.createElement('div');
                        patternItem.className = 'dropdown-item';
                        patternItem.textContent = 'Pattern';

                        patternItem.addEventListener('click', (e) => {
                            e.stopPropagation();

                            const scaleHeader = scaleDropdown.querySelector('.dropdown-header span');
                            scaleHeader.textContent = 'Pattern';

                            scaleDropdownList.querySelectorAll('.dropdown-item').forEach(i => i.classList.remove('selected'));
                            patternItem.classList.add('selected');

                            const scaleDropdownMenu = scaleDropdownList.closest('.dropdown-menu');
                            scaleDropdownMenu.classList.remove('show');
                            scaleDropdownMenu.closest('.snap-dropdown').classList.remove('focused');

                            const customScaleInput = card.querySelector('.tumbler-custom-scale-control-group .custom-scale-input');
                            if (customScaleInput) {
                                customScaleInput.classList.add('disabled');
                                const scaleInput = customScaleInput.querySelector('.scale-input');
                                if (scaleInput) {
                                    scaleInput.disabled = true;
                                    scaleInput.value = '';
                                }
                            }
                        });

                        scaleDropdownList.appendChild(patternItem);
                    }
                }

                const scaleDropdown = card.querySelector('.tumbler-scale-control-group .snap-dropdown');
                simulateDropdownSelection(scaleDropdown, data.scale);

                if (data.scale === 'Custom scale' && data.customScale) {
                    const customScaleInput = card.querySelector('.tumbler-custom-scale-control-group .scale-input');
                    if (customScaleInput) {
                        customScaleInput.value = data.customScale;
                        customScaleInput.dispatchEvent(new Event('input'));
                    }
                }

                const colorDropdown = card.querySelector('.tumbler-colors-control-group .snap-dropdown');
                simulateDropdownSelection(colorDropdown, data.color);

                const priceDropdown = card.querySelector('.tumbler-prices-control-group .snap-dropdown');
                simulateDropdownSelection(priceDropdown, data.price);
            }
        });

        const mugCards = document.querySelectorAll('.mug-products-card');
        mugCards.forEach(card => {
            const headerEl = card.querySelector('.header-text');
            if (!headerEl) return;
            const headerText = headerEl.textContent;
            const data = settings.tumblerProducts[headerText];
            if (data) {

                const toggleBtn = card.querySelector('.toggle-btn');
                const controls = card.querySelector('.mug-products-controls');
                if (data.isActive) {
                    toggleBtn.classList.add('active');
                    toggleBtn.style.backgroundColor = '#470CED';
                    card.classList.remove('off');
                    if (controls) controls.classList.remove('off');
                } else {
                    toggleBtn.classList.remove('active');
                    toggleBtn.style.backgroundColor = '#cfd4d4';
                    card.classList.add('off');
                    if (controls) controls.classList.add('off');
                }

                const sidesDropdown = card.querySelector('.mug-sides-control-group .snap-dropdown');
                simulateDropdownSelection(sidesDropdown, data.sides);

                if (data.sides === 'Two Sides') {
                    const scaleDropdown = card.querySelector('.mug-scale-control-group .snap-dropdown');
                    const scaleDropdownList = scaleDropdown?.querySelector('.dropdown-list');

                    if (scaleDropdownList && !scaleDropdownList.querySelector('.dropdown-item:last-child')?.textContent.includes('Pattern')) {

                        const patternItem = document.createElement('div');
                        patternItem.className = 'dropdown-item';
                        patternItem.textContent = 'Pattern';

                        patternItem.addEventListener('click', (e) => {
                            e.stopPropagation();

                            const scaleHeader = scaleDropdown.querySelector('.dropdown-header span');
                            scaleHeader.textContent = 'Pattern';

                            scaleDropdownList.querySelectorAll('.dropdown-item').forEach(i => i.classList.remove('selected'));
                            patternItem.classList.add('selected');

                            const scaleDropdownMenu = scaleDropdownList.closest('.dropdown-menu');
                            scaleDropdownMenu.classList.remove('show');
                            scaleDropdownMenu.closest('.snap-dropdown').classList.remove('focused');

                            const customScaleInput = card.querySelector('.mug-custom-scale-control-group .custom-scale-input');
                            if (customScaleInput) {
                                customScaleInput.classList.add('disabled');
                                const scaleInput = customScaleInput.querySelector('.scale-input');
                                if (scaleInput) {
                                    scaleInput.disabled = true;
                                    scaleInput.value = '';
                                }
                            }
                        });

                        scaleDropdownList.appendChild(patternItem);
                    }
                }

                const scaleDropdown = card.querySelector('.mug-scale-control-group .snap-dropdown');
                simulateDropdownSelection(scaleDropdown, data.scale);

                if (data.scale === 'Custom scale' && data.customScale) {
                    const customScaleInput = card.querySelector('.mug-custom-scale-control-group .scale-input');
                    if (customScaleInput) {
                        customScaleInput.value = data.customScale;
                        customScaleInput.dispatchEvent(new Event('input'));
                    }
                }

                const colorDropdown = card.querySelector('.mug-colors-control-group .snap-dropdown');
                simulateDropdownSelection(colorDropdown, data.color);

                const priceDropdown = card.querySelector('.mug-prices-control-group .snap-dropdown');
                simulateDropdownSelection(priceDropdown, data.price);
            }
        });

        updateStartAutomationButton();
        const nativeUploaderRadio = document.getElementById('native-uploader');
        const snapUploaderRadio = document.getElementById('snap-uploader');
        if (nativeUploaderRadio && snapUploaderRadio) {
            if (settings.uploaderType === 'native') {
                nativeUploaderRadio.checked = true;
                snapUploaderRadio.checked = false;
            } else {
                nativeUploaderRadio.checked = false;
                snapUploaderRadio.checked = true;
            }
        }
    }

    function simulateDropdownSelection(dropdown, value) {
        if (!dropdown) return;

        const items = dropdown.querySelectorAll('.dropdown-item');
        const targetItem = Array.from(items).find(item => item.textContent.trim() === value);

        if (targetItem) {

            items.forEach(item => item.classList.remove('selected'));

            targetItem.classList.add('selected');

            const header = dropdown.querySelector('.dropdown-header span');
            if (header) header.textContent = value;

            if (dropdown.closest('.color-options-control-group')) {
                const headerContent = dropdown.querySelector('.dropdown-header .header-content');
                const headerColorIndicator = headerContent?.querySelector('.color-indicator');
                const selectedColorIndicator = targetItem.querySelector('.color-indicator');

                if (headerColorIndicator && selectedColorIndicator) {
                    if (value === 'Custom Color') {
                        headerColorIndicator.innerHTML = '<img src="' + chrome.runtime.getURL('assets/colorwheel-ic.svg') + '" alt="Color Wheel">';
                        headerColorIndicator.style.backgroundColor = '';

                        const customColorInput = dropdown.closest('.scalable-products-card')
                            ?.querySelector('.control-group-custom-colors-control-group .custom-color-input');
                        const hexInput = customColorInput?.querySelector('.color-input');

                        if (customColorInput && hexInput) {
                            customColorInput.classList.remove('disabled');
                            hexInput.disabled = false;

                            if (!hexInput.value) {
                                hexInput.value = 'FFFFFF';
                                hexInput.dispatchEvent(new Event('input'));
                            }
                        }
                    } else {
                        headerColorIndicator.innerHTML = '';
                        headerColorIndicator.style.backgroundColor = selectedColorIndicator.style.backgroundColor;

                        const customColorInput = dropdown.closest('.scalable-products-card')
                            ?.querySelector('.control-group-custom-colors-control-group .custom-color-input');
                        const hexInput = customColorInput?.querySelector('.color-input');

                        if (customColorInput && hexInput) {
                            customColorInput.classList.add('disabled');
                            hexInput.disabled = true;
                            hexInput.value = '';
                        }
                    }
                }
            }

            if (dropdown.closest('.scale-options-control-group')) {
                handleScaleOptionChange(value, dropdown);
            } else if (dropdown.closest('.color-options-control-group')) {
                handleColorOptionChange(value, dropdown);
            } else if (dropdown.closest('.tumbler-scale-control-group')) {
                updateTumblerScaleOptions(value);
            }
        }
    }

    function initDropdownHandlers() {
        const dropdowns = document.querySelectorAll('.snap-dropdown');

        dropdowns.forEach(dropdown => {
            const header = dropdown.querySelector('.dropdown-header');
            const menu = dropdown.querySelector('.dropdown-menu');
            const items = dropdown.querySelectorAll('.dropdown-item');

            items.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const value = item.textContent.trim();
                    header.querySelector('span').textContent = value;

                    items.forEach(i => i.classList.remove('selected'));
                    item.classList.add('selected');

                    menu.classList.remove('show');
                    dropdown.classList.remove('focused');

                    if (dropdown.closest('.scale-options-control-group')) {
                        handleScaleOptionChange(value, dropdown);
                    } else if (dropdown.closest('.tumbler-scale-control-group')) {
                        updateTumblerScaleOptions(value);
                    } else if (dropdown.closest('.color-options-control-group')) {
                        handleColorOptionChange(value, dropdown);
                    }
                });
            });
        });
    }

    const customColorStyles = document.createElement("style");
    customColorStyles.id = 'custom-color-styles';
    customColorStyles.textContent = '';
    document.head.appendChild(customColorStyles);

    const additionalStyles = document.createElement("style");
    additionalStyles.id = 'additional-color-styles';
    additionalStyles.textContent = '';
    document.head.appendChild(additionalStyles);

    const additionalColorStyles = document.getElementById('additional-color-styles');
    if (additionalColorStyles) {
        additionalColorStyles.textContent += `
            .custom-color-input.disabled .color-live-indicator {
                background-color: #FFFFFF !important;
            }
        `;
    }

    document.addEventListener('click', (e) => {
        const textSwapLink = e.target.closest('.text-swap-link');
        if (textSwapLink) {
            e.preventDefault();

            window.open('https://drive.google.com/file/d/1pGlS0MEfTy1mzgpIPufCV6iZ_NPHj-5z/view?usp=sharing', '_blank');
        }
    });

    function initUploaderToggles() {
        const nativeUploaderRadio = document.getElementById('native-uploader');
        const snapUploaderRadio = document.getElementById('snap-uploader');
        if (!nativeUploaderRadio || !snapUploaderRadio) return;

        let useNativeUploader = localStorage.getItem('useNativeUploader') === 'true';
        
        nativeUploaderRadio.checked = useNativeUploader;
        snapUploaderRadio.checked = !useNativeUploader;
        
        nativeUploaderRadio.addEventListener('change', function() {
            localStorage.setItem('useNativeUploader', this.checked.toString());
            snapUploaderRadio.checked = !this.checked;
        });
        
        snapUploaderRadio.addEventListener('change', function() {
            localStorage.setItem('useNativeUploader', (!this.checked).toString());
            nativeUploaderRadio.checked = !this.checked;
        });
    }

})();