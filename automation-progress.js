const AutomationProgressUI = (function() {
    class AutomationProgressUI {
        constructor() {
            this.container = null;
            this.fileName = null;
            this.statusText = null;
            this.spinner = null;
            this.completedIcon = null;
            this.counterBubble = null;
            this.percentText = null;
            this.progressFill = null;
            this.completedContainer = null;
            this.mainContent = null;
            this.isVisible = false;
    
            this.createElements();
        }
    
        createElements() {
    
                this.container = document.createElement('div');
                this.container.id = 'snap-automation-progress';
                Object.assign(this.container.style, {
                    position: 'fixed',
                    bottom: '20px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    width: '994px',
                    padding: '24px',
                    backgroundColor: '#470CED',
                    borderRadius: '10px',
                    color: '#ffffff',
                    fontFamily: 'Amazon Ember, sans-serif',
                    zIndex: '9999',
                    display: 'none'
                });
    
                this.mainContent = document.createElement('div');
            this.container.appendChild(this.mainContent);
    
                const header = this.createHeader();
                this.mainContent.appendChild(header);
    
                const progress = this.createProgress();
                this.mainContent.appendChild(progress);
    
                this.completedContainer = this.createCompletedView();
                this.container.appendChild(this.completedContainer);
    
                this.addStyles();
            }
    
            createHeader() {
                const header = document.createElement('div');
                Object.assign(header.style, {
                    display: 'flex',
                    justifyContent: 'space-between',
                    marginBottom: '16px'
                });
    
                const leftSide = document.createElement('div');
                leftSide.style.display = 'flex';
                leftSide.style.gap = '32px';
    
                const fileInfo = document.createElement('div');
                const fileLabel = document.createElement('div');
                fileLabel.textContent = 'Currently Processing';
                fileLabel.style.fontSize = '14px';
                fileLabel.style.marginBottom = '4px';
    
                this.fileName = document.createElement('div');
                this.fileName.style.fontSize = '13px';
    
                fileInfo.appendChild(fileLabel);
                fileInfo.appendChild(this.fileName);
    
                const statusInfo = document.createElement('div');
                const statusLabel = document.createElement('div');
                statusLabel.textContent = 'Status';
                statusLabel.style.fontSize = '14px';
                statusLabel.style.marginBottom = '4px';
    
                const statusRow = document.createElement('div');
                statusRow.style.display = 'flex';
                statusRow.style.alignItems = 'center';
                statusRow.style.gap = '8px';
    
                this.spinner = document.createElement('div');
                Object.assign(this.spinner.style, {
                    width: '10px',
                    height: '10px',
                    border: '2px solid rgba(255,255,255,0.2)',
                    borderTopColor: '#ffffff',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                });
    
                this.statusText = document.createElement('div');
                this.statusText.style.fontSize = '13px';
    
            statusRow.appendChild(this.spinner);
                statusRow.appendChild(this.statusText);
                statusInfo.appendChild(statusLabel);
                statusInfo.appendChild(statusRow);
    
                leftSide.appendChild(fileInfo);
                leftSide.appendChild(statusInfo);
    
                const rightSide = document.createElement('div');
                rightSide.style.display = 'flex';
                rightSide.style.alignItems = 'center';
                rightSide.style.gap = '8px';
    
                const tipIcon = document.createElement('div');
                Object.assign(tipIcon.style, {
                    width: '26px',
                    height: '26px',
                    borderRadius: '50%',
                    backgroundColor: '#3200C0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '14px'
                });
                tipIcon.textContent = '🤝';
    
                const tipBox = document.createElement('div');
                Object.assign(tipBox.style, {
                    backgroundColor: '#3200C0',
                    borderRadius: '4px',
                    padding: '6px 12px',
                    fontSize: '12px'
                });
                tipBox.textContent = 'Avoid interacting with this tab to prevent automation from failing.';
    
                rightSide.appendChild(tipIcon);
                rightSide.appendChild(tipBox);
    
                header.appendChild(leftSide);
                header.appendChild(rightSide);
    
                return header;
            }
    
            createProgress() {
                const progress = document.createElement('div');
                progress.style.marginTop = '16px';
    
                const progressHeader = document.createElement('div');
                progressHeader.style.display = 'flex';
                progressHeader.style.justifyContent = 'space-between';
                progressHeader.style.marginBottom = '8px';
    
                const progressLeft = document.createElement('div');
                progressLeft.style.display = 'flex';
                progressLeft.style.alignItems = 'center';
                progressLeft.style.gap = '8px';
    
                const progressLabel = document.createElement('div');
                progressLabel.textContent = 'Total Progress';
                progressLabel.style.fontSize = '14px';
    
                this.counterBubble = document.createElement('div');
                Object.assign(this.counterBubble.style, {
                    backgroundColor: '#3700a8',
                    borderRadius: '4px',
                    padding: '2px 8px',
                    fontSize: '12px'
                });
    
                progressLeft.appendChild(progressLabel);
                progressLeft.appendChild(this.counterBubble);
    
                this.percentText = document.createElement('div');
                this.percentText.style.fontSize = '14px';
    
                progressHeader.appendChild(progressLeft);
                progressHeader.appendChild(this.percentText);
    
                const progressBar = document.createElement('div');
                Object.assign(progressBar.style, {
                    width: '100%',
                    height: '5px',
                    backgroundColor: '#ffffff',
                    borderRadius: '4px',
                    overflow: 'hidden',
                    marginTop: '8px'
                });
    
                this.progressFill = document.createElement('div');
                Object.assign(this.progressFill.style, {
                    width: '0%',
                    height: '100%',
                    backgroundColor: '#01BB87',
                    borderRadius: '4px',
                    transition: 'width 0.3s ease-in-out'
                });
    
                progressBar.appendChild(this.progressFill);
                progress.appendChild(progressHeader);
                progress.appendChild(progressBar);
    
                return progress;
            }
    
            createCompletedView() {
                const completed = document.createElement('div');
                Object.assign(completed.style, {
                    display: 'none',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '8px',
                    padding: '16px'
                });
    
                const icon = document.createElement('img');
                icon.src = chrome.runtime.getURL('assets/completed.svg');
                icon.style.width = '24px';
                icon.style.height = '24px';
    
                const text = document.createElement('div');
                text.textContent = 'Completed';
                text.style.fontSize = '16px';
    
                completed.appendChild(icon);
                completed.appendChild(text);
    
                return completed;
            }
    
            addStyles() {
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                `;
                document.head.appendChild(style);
            }
    
        show() {
            if (!this.isVisible) {
                document.body.appendChild(this.container);
                    this.container.style.display = 'block';
                this.isVisible = true;
                }
        }
    
        hide() {
            if (this.isVisible) {
                    this.container.style.display = 'none';
                if (this.container.parentNode) {
                    this.container.parentNode.removeChild(this.container);
                }
                this.isVisible = false;
            }
        }
    
        updateFileName(filename) {
            if (this.fileName) {
                this.fileName.textContent = filename;
            }
        }
    
        updateStatus(status) {
            if (this.statusText) {
                const previousStatus = this.statusText.textContent;
                this.statusText.textContent = status;
    
                if (status === 'Completed') {
                    if (this.spinner) {
                        this.spinner.style.display = 'none';
                    }
    
                    if (!this.completedIcon) {
                        this.completedIcon = document.createElement('img');
                        this.completedIcon.src = chrome.runtime.getURL('assets/completed.svg');
                        this.completedIcon.style.width = '14px';
                        this.completedIcon.style.height = '14px';
    
                        const statusRow = this.spinner.parentNode;
                        statusRow.insertBefore(this.completedIcon, this.statusText);
                    } else {
                        this.completedIcon.style.display = 'block';
                    }
                } else {
                    if (this.spinner) {
                        this.spinner.style.display = 'block';
                    }
    
                    if (this.completedIcon) {
                        this.completedIcon.style.display = 'none';
                    }
                }
            }
        }
    
            updateProgress(current, total, isTabCompleted = false, forceProgressUpdate = false, statusInfo = null) {
            if (this.counterBubble && this.percentText && this.progressFill) {
                let percentage, displayPercentage;
    
                const forceExact100Percent = statusInfo && statusInfo.forceExact100Percent;
    
                if (forceExact100Percent) {
                    displayPercentage = 100;
                }
    
                else if (statusInfo && statusInfo.statusIndex !== undefined && statusInfo.totalStatuses !== undefined) {
                    const percentPerFile = 100 / total;
                    const completedFilesPercentage = (current - 1) * percentPerFile;
                    const percentPerStatus = percentPerFile / statusInfo.totalStatuses;
    
                    let currentFilePercentage;
                    if (statusInfo.statusIndex === 0) {
                        currentFilePercentage = percentPerFile * 0.05;
                    } else if (statusInfo.isCompleted) {
                        currentFilePercentage = percentPerFile;
                    } else {
                        currentFilePercentage = statusInfo.statusIndex * percentPerStatus;
                    }
    
                    displayPercentage = completedFilesPercentage + currentFilePercentage;
    
                    if (displayPercentage >= 100 && !statusInfo.isCompleted) {
                        displayPercentage = 99.5; 
                    }
    
                    if (current === total && statusInfo.isCompleted) {
                        displayPercentage = 100;
                    }
    
                    if (current < total && statusInfo.isCompleted) {
                        displayPercentage = current * percentPerFile;
                    }
                }
                else if (isTabCompleted || forceProgressUpdate) {
                    const percentPerFile = 100 / total;
                    percentage = Math.floor(((current - 1) / total) * 100);
                    displayPercentage = percentage + percentPerFile;
    
                    if (current >= total && forceProgressUpdate) {
                        displayPercentage = 100;
                    }
    
                    if (current < total && forceProgressUpdate) {
                        displayPercentage = current * percentPerFile;
                    }
                } else {
                    const percentPerFile = 100 / total;
                    percentage = ((current - 1) / total) * 100;
                    const partialProgress = percentPerFile * 0.05; 
                    displayPercentage = percentage + partialProgress;
                }
    
                const clamp = v => Math.max(0, Math.min(100, v));
                const clamped = clamp(displayPercentage);

                this.counterBubble.textContent = `${Math.min(current, total)}/${total}`;
                clearTimeout(this._progressDebounceTimer);
                this._progressDebounceTimer = setTimeout(() => {
                    this.percentText.textContent = `${Math.round(clamped)}%`;
                    this.progressFill.style.transition = 'width 0.5s ease-in-out';
                    this.progressFill.style.width = `${clamped}%`;
                }, 80);
    
                if (displayPercentage >= 100) {
                    this.progressFill.style.backgroundColor = '#01BB87'; 
                } else if (displayPercentage >= 75) {
                    this.progressFill.style.backgroundColor = '#01BB87'; 
                } else if (displayPercentage >= 50) {
                    this.progressFill.style.backgroundColor = '#4CAF50'; 
                } else if (displayPercentage >= 25) {
                    this.progressFill.style.backgroundColor = '#8BC34A'; 
                } else {
                    this.progressFill.style.backgroundColor = '#01BB87'; 
                }
            }
        }
    
        showCompleted() {
            if (this.mainContent && this.completedContainer) {
                    this.mainContent.style.display = 'none';
                    this.completedContainer.style.display = 'flex';
                }
            }
    
        resetToNormal() {
            if (this.mainContent && this.completedContainer) {
                    this.mainContent.style.display = 'block';
                    this.completedContainer.style.display = 'none';
                }
            }
        }
    
        return AutomationProgressUI;
    })();
    
    window.AutomationProgressUI = AutomationProgressUI;
    
    const automationProgressUI = new AutomationProgressUI();
    
    function initializeProgressUI() {
        const urlParams = new URLSearchParams(window.location.search);
        const snapParam = urlParams.get('Snap');
    
        if (snapParam !== null) {
            chrome.runtime.sendMessage({ action: 'getAutomationState' }, (response) => {
                if (response && response.success && response.state) {
                    const state = response.state;
    
                    if (state.currentFile && state.currentFile.status !== 'completed') {
                        automationProgressUI.show();
    
                        if (state.currentFile) {
                            automationProgressUI.updateFileName(state.currentFile.filename || 'Unknown file');
                            automationProgressUI.updateStatus('Uploading...');
    
                            const currentFileIndex = state.currentFile.index !== undefined ? state.currentFile.index + 1 : 1;
                            const totalFiles = state.totalFiles || 1;
    
                            automationProgressUI.updateProgress(currentFileIndex, totalFiles);
                        }
                    }
                }
            });
        }
    }
    
    document.addEventListener('DOMContentLoaded', initializeProgressUI);
    
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        initializeProgressUI();
    }
    
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === "updateAutomationProgress") {
            const urlParams = new URLSearchParams(window.location.search);
            const snapParam = urlParams.get('Snap');
    
            if (snapParam === null) {
                sendResponse({ success: true });
                return true;
            }
    
            if (message.forceHide || message.isTabCompleted) {
                automationProgressUI.hide();
                sendResponse({ success: true, action: 'hide' });
                return true;
            }
    
            automationProgressUI.show();
    
            if (message.filename) {
                automationProgressUI.updateFileName(message.filename);
            }
    
            if (message.status) {
                automationProgressUI.updateStatus(message.status);
            }
    
            if (message.currentFile && message.totalFiles) {
                const forceCompleted = message.updateProgressBar === true;
                let statusInfo = null;
    
                if (message.queueProgress) {
                    const queueProgress = message.queueProgress;
                    const processedPercentage = (queueProgress.processed / queueProgress.total) * 100;
                    const currentProgress = (queueProgress.current / queueProgress.total) * 100;
                }
    
                if (message.statusInfo) {
                    statusInfo = message.statusInfo;
    
                    if (message.forceExact100Percent) {
                        statusInfo.forceExact100Percent = true;
                    }
                } else if (message.forceExact100Percent) {
                    statusInfo = {
                        forceExact100Percent: true
                    };
                }
    
                automationProgressUI.updateProgress(
                    message.currentFile, 
                    message.totalFiles,
                    forceCompleted || message.isTabCompleted || false,
                    message.forceProgressUpdate || false, 
                    statusInfo 
                );
            }
    
            if (message.completed) {
                automationProgressUI.showCompleted();
            } else {
                automationProgressUI.resetToNormal();
            }
    
            sendResponse({ success: true, statusUpdated: message.status });
        } else if (message.action === "hideAutomationProgress") {
            automationProgressUI.hide();
            sendResponse({ success: true });
        } else if (message.action === "playCompletionSound") {
            if (message.isFinalTab) {
                const COMPLETION_SOUND_URL = chrome.runtime.getURL('assets/aut-completed.wav');
                let completionAudio = null;
    
                const playSound = () => {
                    return new Promise((resolve, reject) => {
                        try {
                            completionAudio = new Audio(COMPLETION_SOUND_URL);
    
                            completionAudio.play()
                                .then(resolve)
                                .catch(error => {
                                    playWithTemporaryAudio().then(resolve).catch(reject);
                                });
                        } catch (error) {
                            playWithTemporaryAudio().then(resolve).catch(reject);
                        }
                    });
                };
    
                const playWithTemporaryAudio = () => {
                    return new Promise((resolve, reject) => {
                        try {
                            const audio = document.createElement('audio');
                            audio.src = COMPLETION_SOUND_URL;
    
                            audio.onended = () => {
                                document.body.removeChild(audio);
                                resolve(true);
                            };
    
                            audio.onerror = (error) => {
                                document.body.removeChild(audio);
                                reject(error);
                            };
    
                            document.body.appendChild(audio);
                            audio.play().catch(reject);
                        } catch (error) {
                            reject(error);
                        }
                    });
                };
    
                playSound()
                    .then(() => {
                        sendResponse({ success: true });
                    })
                    .catch(error => {
                        sendResponse({ success: false, error: error.toString() });
                    });
            } else {
                sendResponse({ success: true, skipped: true });
            }
    
            return true; 
        }
    
        return true; 
    });