(function () {
    "use strict";
    if (!document.getElementById("clothing-style")) {
      const clothingStyleSheet = document.createElement("style");
      clothingStyleSheet.id = "clothing-style";
      clothingStyleSheet.textContent = `
        .apply-btn-container {
            padding: 0 !important;
            margin: 10px 0 0 0 !important;
        }
    
        .resize-btn {
            all: unset !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 8px !important;
            padding: 0 16px !important;
            height: 44px !important;
            width: 100% !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            background-color: #470CED !important;
            color: #FFFFFF !important;
            font-size: 14px !important;
            border: none !important;
            box-shadow: none !important;
            box-sizing: border-box !important;
        }
        
        .resize-btn:hover:not(:disabled) {
            background-color: #2A00A0 !important;
        }
        
        .resize-btn:disabled {
            background-color: #cfd4d4 !important;
            color: rgba(0, 0, 0, 0.5) !important;
            cursor: not-allowed !important;
        }
    
        .resize-btn img {
            width: 20px !important;
            height: 20px !important;
            margin-right: 10px !important;
            opacity: 1 !important;
            transition: none !important;
            filter: brightness(0) invert(1) !important;
        }
    
        .resize-btn:disabled img {
            opacity: 0.5 !important;
            transition: none !important;
            filter: brightness(0) !important;
        }

        .tab-container {
            margin-bottom: 20px !important;
            display: flex !important;
            width: 100% !important;
        }

        .tab-button {
            flex: 1 !important;
            padding: 8px 16px !important;
            border: 1px solid #470CED !important;
            background-color: #ffffff !important;
            color: #470CED !important;
            cursor: pointer !important;
            outline: none !important;
            margin: 0 !important;
            border-radius: 0 !important;
            text-align: center !important;
            border-right: none !important;
            font-size: 13px !important;
            transition: background-color 0.3s ease, color 0.3s ease !important;
        }

        .tab-button:last-child {
            border-right: 1px solid #470CED !important;
        }

        .tab-button.active {
            background-color: #470CED !important;
            color: #ffffff !important;
        }
      `;
      document.head.appendChild(clothingStyleSheet);
    }
  
    let preferences = {};
    const processingFlags = {};
  
    try {
        const savedPreferences = localStorage.getItem("clothingResizePreferences");
        if (savedPreferences) {
            preferences = JSON.parse(savedPreferences);
        }
    } catch (error) {
        console.error();
    }

    const crc32Table = (() => {
      let table;
      const crcTable = [];
      for (let i = 0; i < 256; i++) {
        table = i;
        for (let j = 0; j < 8; j++) {
          table = table & 1 ? 3988292384 ^ (table >>> 1) : table >>> 1;
        }
        crcTable[i] = table;
      }
      return crcTable;
    })();

    async function setDPI(blob, dpi) {
      return new Promise(resolve => {
        const img = new Image();
        const objectUrl = URL.createObjectURL(blob);
        img.crossOrigin = "Anonymous";
        img.src = objectUrl;
        img.onload = async () => {
          URL.revokeObjectURL(objectUrl);
          
          const buffer = await blob.arrayBuffer();
          const data = new Uint8Array(buffer);
          
          const pngSignature = [137, 80, 78, 71, 13, 10, 26, 10];
          for (let i = 0; i < pngSignature.length; i++) {
            if (data[i] !== pngSignature[i]) {
              return resolve(blob);
            }
          }
          
          const dpiValue = Math.round(dpi * 39.3701);
          const pHYsData = new Uint8Array([
            dpiValue >>> 24 & 255, dpiValue >>> 16 & 255, dpiValue >>> 8 & 255, dpiValue & 255,
            dpiValue >>> 24 & 255, dpiValue >>> 16 & 255, dpiValue >>> 8 & 255, dpiValue & 255,
            1 
          ]);
          

          const chunkType = new Uint8Array([112, 72, 89, 115]);
          
          const crc = calculateCRC32([...chunkType, ...pHYsData]);
          
          const chunk = new Uint8Array([
            0, 0, 0, 9,
            ...chunkType,
            ...pHYsData,
            crc >>> 24 & 255, crc >>> 16 & 255, crc >>> 8 & 255, crc & 255
          ]);
          
          const newData = new Uint8Array(data.length + chunk.length);
          newData.set(data.slice(0, 33), 0);
          newData.set(chunk, 33);
          newData.set(data.slice(33), 33 + chunk.length);
          
          const newBlob = new Blob([newData], { type: "image/png" });
          resolve(newBlob);
        };
        
        img.onerror = () => {
          URL.revokeObjectURL(objectUrl);
          resolve(blob);
        };
      });
    }

    function calculateCRC32(data) {
      let crc = -1;
      for (let i = 0; i < data.length; i++) {
        crc = (crc >>> 8) ^ crc32Table[(crc ^ data[i]) & 0xFF];
      }
      return (~crc >>> 0);
    }

    async function compressImage(blob) {
      if (blob.size <= 10485760) {
        return blob;
      }
      
      try {
        console.log();
        
        let compressionLevel = 1024;
        let compressedBlob = null;
        let attempts = 0;
        const MAX_ATTEMPTS = 5;
        
        while (attempts < MAX_ATTEMPTS) {
          attempts++;
          
          let buffer = await blob.arrayBuffer();
          let decoded = UPNG.decode(buffer);
          let rgba8 = UPNG.toRGBA8(decoded)[0];
          let encoded = UPNG.encode([rgba8], decoded.width, decoded.height, compressionLevel);
          compressedBlob = new Blob([encoded], {type: "image/png"});
          
          console.log();
          
          if (compressedBlob.size <= 20971520 || compressionLevel <= 128) {
            break;
          }
          
          compressionLevel = Math.floor(compressionLevel / 2);
        }
        
        console.log();
        return compressedBlob;
      } catch (error) {
        console.error();
        return blob;
      }
    }

    async function getCurrentDesign() {
      return new Promise((resolve, reject) => {
        const images = document.querySelectorAll('img[alt$=".png"], img[alt="null"]');
        if (images.length === 0) {
          reject("No design image found");
          return;
        }

        let targetImage = Array.from(images).find(img => img.getAttribute("alt").endsWith(".png"));
        if (!targetImage) {
          targetImage = Array.from(images).find(img => img.getAttribute("alt") === "null");
        }

        if (!targetImage) {
          reject("No suitable design image found");
          return;
        }

        const imgSrc = targetImage.getAttribute("src");
        if (!imgSrc) {
          reject("Image source not found");
          return;
        }

        const img = new Image();
        img.crossOrigin = "Anonymous";
        img.src = imgSrc;

        img.onload = () => {
          const canvas = document.createElement("canvas");
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext("2d", { willReadFrequently: true });
          ctx.drawImage(img, 0, 0);
          resolve(canvas);
        };

        img.onerror = (error) => {
          reject("Error loading image: " + error);
        };
      });
    }

    async function trimDesign(canvas) {
      return new Promise((resolve) => {
        const ctx = canvas.getContext("2d", { willReadFrequently: true });
        const pixels = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = pixels.data;
        let top = null;
        let left = null;
        let right = null;
        let bottom = null;

        for (let y = 0; y < canvas.height; y++) {
          for (let x = 0; x < canvas.width; x++) {
            const alpha = data[((y * canvas.width) + x) * 4 + 3];
            if (alpha !== 0) {
              top = y;
              break;
            }
          }
          if (top !== null) break;
        }

        for (let y = canvas.height - 1; y >= 0; y--) {
          for (let x = 0; x < canvas.width; x++) {
            const alpha = data[((y * canvas.width) + x) * 4 + 3];
            if (alpha !== 0) {
              bottom = y;
              break;
            }
          }
          if (bottom !== null) break;
        }

        for (let x = 0; x < canvas.width; x++) {
          for (let y = 0; y < canvas.height; y++) {
            const alpha = data[((y * canvas.width) + x) * 4 + 3];
            if (alpha !== 0) {
              left = x;
              break;
            }
          }
          if (left !== null) break;
        }

        for (let x = canvas.width - 1; x >= 0; x--) {
          for (let y = 0; y < canvas.height; y++) {
            const alpha = data[((y * canvas.width) + x) * 4 + 3];
            if (alpha !== 0) {
              right = x;
              break;
            }
          }
          if (right !== null) break;
        }

        const trimmed = document.createElement("canvas");
        const trimmedCtx = trimmed.getContext("2d", { willReadFrequently: true });
        const trimmedWidth = right - left + 1;
        const trimmedHeight = bottom - top + 1;
        trimmed.width = trimmedWidth;
        trimmed.height = trimmedHeight;

        trimmedCtx.drawImage(
          canvas,
          left, top, trimmedWidth, trimmedHeight,
          0, 0, trimmedWidth, trimmedHeight
        );

        resolve(trimmed);
      });
    }

    async function composeImage(canvas, product, side) {
      return new Promise((resolve) => {
        const composed = document.createElement("canvas");
        const ctx = composed.getContext("2d", { willReadFrequently: true });

        if (side === "FRONT") {
          composed.width = product.FRONTCanvasDimensions.width;
          composed.height = product.FRONTCanvasDimensions.height;

          const pocketWidth = 1800;
          const pocketHeight = 1620;
          const scale = Math.min(
            pocketWidth / canvas.width,
            pocketHeight / canvas.height
          );

          const scaledWidth = canvas.width * scale;
          const scaledHeight = canvas.height * scale;
          const x = composed.width - scaledWidth - 200;
          const y = 400;

          ctx.drawImage(canvas, x, y, scaledWidth, scaledHeight);
        } else {
          composed.width = product.BACKCanvasDimensions.width;
          composed.height = product.BACKCanvasDimensions.height;
          const x = (composed.width - canvas.width) / 2;
          const y = (composed.height - canvas.height) / 2;
          ctx.drawImage(canvas, x, y);
        }

        resolve(composed);
      });
    }

    async function waitForElement(selector, timeout = 60000) {
      return new Promise((resolve, reject) => {
        const element = document.querySelector(selector);
        if (element && element.offsetParent !== null) {
          resolve(element);
          return;
        }

        const observer = new MutationObserver((mutations) => {
          const element = document.querySelector(selector);
          if (element && element.offsetParent !== null) {
            observer.disconnect();
            resolve(element);
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true,
          attributes: true
        });

        setTimeout(() => {
          observer.disconnect();
          reject(new Error(`Element ${selector} not found within ${timeout}ms`));
        }, timeout);
      });
    }

    async function clickIfExists(selector) {
      const element = document.querySelector(selector);
      if (element) {
        element.click();
        await delay(300);
        return true;
      }
      return false;
    }

    async function uploadToInput(design, inputSelector) {
      return new Promise((resolve, reject) => {
        const label = document.querySelector(inputSelector);
        if (!label) {
          reject(new Error("Upload input not found"));
          return;
        }

        const inputId = label.getAttribute("for");
        const input = document.getElementById(inputId);
        if (!input) {
          reject(new Error("File input not found"));
          return;
        }

        design.toBlob(async (blob) => {
          try {
            let processedBlob = await compressImage(blob);
            
            processedBlob = await setDPI(processedBlob, 300);
            
            const transfer = new DataTransfer();
            const file = new File([processedBlob], "design.png", { type: "image/png" });
            transfer.items.add(file);
            input.files = transfer.files;
            const event = new Event("change", { bubbles: true });
            input.dispatchEvent(event);
            await delay(300);
            resolve();
          } catch (error) {
            console.error();
            const transfer = new DataTransfer();
            const file = new File([blob], "design.png", { type: "image/png" });
            transfer.items.add(file);
            input.files = transfer.files;
            const event = new Event("change", { bubbles: true });
            input.dispatchEvent(event);
            await delay(300);
            resolve();
          }
        }, "image/png", 1);
      });
    }

    async function handleBackUpload(product) {
      try {
        const frontTabSelector = document.querySelector(".btn.btn-outline-primary.btn-FRONT.ng-star-inserted");
        if (frontTabSelector) {
          frontTabSelector.click();
          await delay(1000);
          await clickIfExists(".delete-button");
          await delay(300);
        }

        const backTabSelector = document.querySelector(".btn.btn-outline-primary.btn-BACK.ng-star-inserted");
        if (!backTabSelector) {
          throw new Error("Back tab selector not found");
        }
        backTabSelector.click();
        await delay(1000);

        await clickIfExists(".delete-button");
        await delay(300);

        const backInput = await waitForElement(product.uploadInputSelector.back);
        if (!backInput) {
          throw new Error("Back upload input not found");
        }
        await delay(300);

        const design = await getCurrentDesign();
        await delay(300);
        
        await uploadToInput(design, product.uploadInputSelector.back);
        await delay(300);

      } catch (error) {
        console.error();
        throw error;
      }
    }

    async function handleFrontUpload(product) {
      try {
        const frontTabSelector = document.querySelector(".btn.btn-outline-primary.btn-FRONT.ng-star-inserted");
        if (!frontTabSelector) {
          throw new Error("Front tab selector not found");
        }
        frontTabSelector.click();
        await delay(1000);

        await clickIfExists(".delete-button");
        await delay(300);

        let design = await getCurrentDesign();
        await delay(300);
        
        design = await trimDesign(design);
        await delay(300);
        
        design = await composeImage(design, product, "FRONT");
        await delay(300);

        const frontInput = await waitForElement(product.uploadInputSelector.front);
        if (!frontInput) {
          throw new Error("Front upload input not found");
        }
        await delay(300);

        await uploadToInput(design, product.uploadInputSelector.front);
        await delay(300);

      } catch (error) {
        console.error();
        throw error;
      }
    }

    async function handleBothSidesUpload(product) {
      try {
        const frontTabSelector = document.querySelector(".btn.btn-outline-primary.btn-FRONT.ng-star-inserted");
        if (!frontTabSelector) {
          throw new Error("Front tab selector not found");
        }
        frontTabSelector.click();
        await delay(1000);

        await clickIfExists(".delete-button");
        await delay(300);

        let design = await getCurrentDesign();
        await delay(300);
        
        design = await trimDesign(design);
        await delay(300);
        
        design = await composeImage(design, product, "FRONT");
        await delay(300);

        const frontInput = await waitForElement(product.uploadInputSelector.front);
        if (!frontInput) {
          throw new Error("Front upload input not found");
        }
        await delay(300);

        await uploadToInput(design, product.uploadInputSelector.front);
        await delay(1000);

        await waitForElement(".delete-button");
        await delay(1000);

        const backTabSelector = document.querySelector(".btn.btn-outline-primary.btn-BACK.ng-star-inserted");
        if (!backTabSelector) {
          throw new Error("Back tab selector not found");
        }
        backTabSelector.click();
        await delay(1000);

        await clickIfExists(".delete-button");
        await delay(300);

        const backInput = await waitForElement(product.uploadInputSelector.back);
        if (!backInput) {
          throw new Error("Back upload input not found");
        }
        await delay(300);

        const backDesign = await getCurrentDesign();
        await delay(300);
        
        await uploadToInput(backDesign, product.uploadInputSelector.back);
        await delay(300);

      } catch (error) {
        console.error();
        throw error;
      }
    }

    async function handleUpload(product, selectedTab) {
      try {
        switch (selectedTab) {
          case "Back":
            await handleBackUpload(product);
            break;
          case "Front (Pocket)":
            await handleFrontUpload(product);
            break;
          case "Back & Pocket":
            await handleBothSidesUpload(product);
            break;
        }
      } catch (error) {
        console.error();
        throw error;
      }
    }
  
    const products = [
      {
        name: "STANDARD_TSHIRT",
        displayName: "Standard T-shirt",
        containerHeading: "Backside & Frontside Options:",
        ctaButtonText: {
          "Back": "Transfer & Optimize Design to Backside",
          "Front (Pocket)": "Apply & Optimize Design to Frontside (Pocket)",
          "Back & Pocket": "Apply & Optimize Design to Both Sides"
        },
        cardId: "STANDARD_TSHIRT-card",
        editButtonClass: "STANDARD_TSHIRT-edit-btn",
        resizeContainerClass: "standard-resize-container",
        uploadInputSelector: {
          back: 'label.file-upload-input[for="STANDARD_TSHIRT-BACK-wizzy"]',
          front: 'label.file-upload-input[for="STANDARD_TSHIRT-FRONT-wizzy"]'
        },
        tabs: ["Back", "Front (Pocket)", "Back & Pocket"],
        FRONTCanvasDimensions: { width: 4500, height: 5400 },
        BACKCanvasDimensions: { width: 4500, height: 5400 },
        sidestabSelector: {
          back: "btn btn-outline-primary btn-BACK ng-star-inserted",
          front: "btn btn-outline-primary btn-FRONT ng-star-inserted"
        },
        injectPosition: "second"
      },
      {
        name: "ZIP_HOODIE",
        displayName: "Zip Hoodie",
        containerHeading: "Backside & Frontside Options:",
        ctaButtonText: {
            "Back": "Transfer & Optimize Design to Backside",
            "Front (Pocket)": "Apply & Optimize Design to Frontside (Pocket)",
            "Back & Pocket": "Apply & Optimize Design to Both Sides"
        },
        cardId: "ZIP_HOODIE-card",
        editButtonClass: "ZIP_HOODIE-edit-btn",
        resizeContainerClass: "zip-resize-container",
        uploadInputSelector: {
          back: 'label.file-upload-input[for="ZIP_HOODIE-BACK-wizzy"]',
          front: 'label.file-upload-input[for="ZIP_HOODIE-FRONT-wizzy"]'
        },
        tabs: ["Back", "Front (Pocket)", "Back & Pocket"],
        FRONTCanvasDimensions: { width: 4500, height: 4050 },
        BACKCanvasDimensions: { width: 4500, height: 5400 },
        sidestabSelector: {
          back: "btn btn-outline-primary btn-BACK ng-star-inserted",
          front: "btn btn-outline-primary btn-FRONT ng-star-inserted"
        },
        injectPosition: "second"
      },
      {
        name: "STANDARD_PULLOVER_HOODIE",
        displayName: "Pullover Hoodie",
        containerHeading: "Backside & Frontside Options:",
        ctaButtonText: {
            "Back": "Transfer & Optimize Design to Backside",
            "Front (Pocket)": "Apply & Optimize Design to Frontside (Pocket)",
            "Back & Pocket": "Apply & Optimize Design to Both Sides"
        },
        cardId: "STANDARD_PULLOVER_HOODIE-card",
        editButtonClass: "STANDARD_PULLOVER_HOODIE-edit-btn",
        resizeContainerClass: "pullover-resize-container",
        uploadInputSelector: {
          back: 'label.file-upload-input[for="STANDARD_PULLOVER_HOODIE-BACK-wizzy"]',
          front: 'label.file-upload-input[for="STANDARD_PULLOVER_HOODIE-FRONT-wizzy"]'
        },
        tabs: ["Back", "Front (Pocket)", "Back & Pocket"],
        FRONTCanvasDimensions: { width: 4500, height: 5400 },
        BACKCanvasDimensions: { width: 4500, height: 5400 },
        sidestabSelector: {
          back: "btn btn-outline-primary btn-BACK ng-star-inserted",
          front: "btn btn-outline-primary btn-FRONT ng-star-inserted"
        },
        injectPosition: "second"
      },
      {
        name: "STANDARD_SWEATSHIRT",
        displayName: "Standard Sweatshirt",
        containerHeading: "Backside & Frontside Options:",
        ctaButtonText: {
            "Back": "Transfer & Optimize Design to Backside",
            "Front (Pocket)": "Apply & Optimize Design to Frontside (Pocket)",
            "Back & Pocket": "Apply & Optimize Design to Both Sides"
        },
        cardId: "STANDARD_SWEATSHIRT-card",
        editButtonClass: "STANDARD_SWEATSHIRT-edit-btn",
        resizeContainerClass: "sweatshirt-resize-container",
        uploadInputSelector: {
          back: 'label.file-upload-input[for="STANDARD_SWEATSHIRT-BACK-wizzy"]',
          front: 'label.file-upload-input[for="STANDARD_SWEATSHIRT-FRONT-wizzy"]'
        },
        tabs: ["Back", "Front (Pocket)", "Back & Pocket"],
        FRONTCanvasDimensions: { width: 4500, height: 5400 },
        BACKCanvasDimensions: { width: 4500, height: 5400 },
        sidestabSelector: {
          back: "btn btn-outline-primary btn-BACK ng-star-inserted",
          front: "btn btn-outline-primary btn-FRONT ng-star-inserted"
        },
        injectPosition: "second"
      },
      {
        name: "RAGLAN",
        displayName: "Raglan",
        containerHeading: "Backside & Frontside Options:",
        ctaButtonText: {
            "Back": "Transfer & Optimize Design to Backside",
            "Front (Pocket)": "Apply & Optimize Design to Frontside (Pocket)",
            "Back & Pocket": "Apply & Optimize Design to Both Sides"
        },
        cardId: "RAGLAN-card",
        editButtonClass: "RAGLAN-edit-btn",
        resizeContainerClass: "raglan-resize-container",
        uploadInputSelector: {
          back: 'label.file-upload-input[for="RAGLAN-BACK-wizzy"]',
          front: 'label.file-upload-input[for="RAGLAN-FRONT-wizzy"]'
        },
        tabs: ["Back", "Front (Pocket)", "Back & Pocket"],
        FRONTCanvasDimensions: { width: 4500, height: 5400 },
        BACKCanvasDimensions: { width: 4500, height: 5400 },
        sidestabSelector: {
          back: "btn btn-outline-primary btn-BACK ng-star-inserted",
          front: "btn btn-outline-primary btn-FRONT.ng-star-inserted"
        },
        injectPosition: "second"
      },
      {
        name: "STANDARD_LONG_SLEEVE",
        displayName: "Long Sleeve Shirt",
        containerHeading: "Backside & Frontside Options:",
        ctaButtonText: {
            "Back": "Transfer & Optimize Design to Backside",
            "Front (Pocket)": "Apply & Optimize Design to Frontside (Pocket)",
            "Back & Pocket": "Apply & Optimize Design to Both Sides"
        },
        cardId: "STANDARD_LONG_SLEEVE-card",
        editButtonClass: "STANDARD_LONG_SLEEVE-edit-btn",
        resizeContainerClass: "long-resize-container",
        uploadInputSelector: {
          back: 'label.file-upload-input[for="STANDARD_LONG_SLEEVE-BACK-wizzy"]',
          front: 'label.file-upload-input[for="STANDARD_LONG_SLEEVE-FRONT-wizzy"]'
        },
        tabs: ["Back", "Front (Pocket)", "Back & Pocket"],
        FRONTCanvasDimensions: { width: 4500, height: 5400 },
        BACKCanvasDimensions: { width: 4500, height: 5400 },
        sidestabSelector: {
          back: "btn btn-outline-primary btn-BACK.ng-star-inserted",
          front: "btn btn-outline-primary btn-FRONT.ng-star-inserted"
        },
        injectPosition: "second"
      },
      {
        name: "TANK_TOP",
        displayName: "Tank Top",
        containerHeading: "Backside & Frontside Options:",
        ctaButtonText: {
            "Back": "Transfer & Optimize Design to Backside",
            "Front (Pocket)": "Apply & Optimize Design to Frontside (Pocket)",
            "Back & Pocket": "Apply & Optimize Design to Both Sides"
        },
        cardId: "TANK_TOP-card",
        editButtonClass: "TANK_TOP-edit-btn",
        resizeContainerClass: "tank-resize-container",
        uploadInputSelector: {
          back: 'label.file-upload-input[for="TANK_TOP-BACK-wizzy"]',
          front: 'label.file-upload-input[for="TANK_TOP-FRONT-wizzy"]'
        },
        tabs: ["Back", "Front (Pocket)", "Back & Pocket"],
        FRONTCanvasDimensions: { width: 4500, height: 5400 },
        BACKCanvasDimensions: { width: 4500, height: 5400 },
        sidestabSelector: {
          back: "btn btn-outline-primary btn-BACK.ng-star-inserted",
          front: "btn btn-outline-primary btn-FRONT.ng-star-inserted"
        },
        injectPosition: "second"
      },
      {
        name: "VNECK",
        displayName: "V-neck T-shirt",
        containerHeading: "Backside & Frontside Options:",
        ctaButtonText: {
            "Back": "Transfer & Optimize Design to Backside",
            "Front (Pocket)": "Apply & Optimize Design to Frontside (Pocket)",
            "Back & Pocket": "Apply & Optimize Design to Both Sides"
        },
        cardId: "VNECK-card",
        editButtonClass: "VNECK-edit-btn",
        resizeContainerClass: "vneck-resize-container",
        uploadInputSelector: {
          back: 'label.file-upload-input[for="VNECK-BACK-wizzy"]',
          front: 'label.file-upload-input[for="VNECK-FRONT-wizzy"]'
        },
        tabs: ["Back", "Front (Pocket)", "Back & Pocket"],
        FRONTCanvasDimensions: { width: 4500, height: 5400 },
        BACKCanvasDimensions: { width: 4500, height: 5400 },
        sidestabSelector: {
          back: "btn btn-outline-primary btn-BACK.ng-star-inserted",
          front: "btn btn-outline-primary btn-FRONT.ng-star-inserted"
        },
        injectPosition: "second"
      },
      {
        name: "PREMIUM_TSHIRT",
        displayName: "Premium T-shirt",
        containerHeading: "Backside & Frontside Options:",
        ctaButtonText: {
            "Back": "Transfer & Optimize Design to Backside",
            "Front (Pocket)": "Apply & Optimize Design to Frontside (Pocket)",
            "Back & Pocket": "Apply & Optimize Design to Both Sides"
        },
        cardId: "PREMIUM_TSHIRT-card",
        editButtonClass: "PREMIUM_TSHIRT-edit-btn",
        resizeContainerClass: "premium-resize-container",
        uploadInputSelector: {
          back: 'label.file-upload-input[for="PREMIUM_TSHIRT-BACK-wizzy"]',
          front: 'label.file-upload-input[for="PREMIUM_TSHIRT-FRONT-wizzy"]'
        },
        tabs: ["Back", "Front (Pocket)", "Back & Pocket"],
        FRONTCanvasDimensions: { width: 4500, height: 5400 },
        BACKCanvasDimensions: { width: 4500, height: 5400 },
        sidestabSelector: {
          back: "btn btn-outline-primary btn-BACK.ng-star-inserted",
          front: "btn btn-outline-primary btn-FRONT.ng-star-inserted"
        },
        injectPosition: "second"
      },
      {
        name: "OVERSIZED_TSHIRT",
        displayName: "Comfort Colors Heavyweight T-shirt",
        containerHeading: "Backside & Frontside Options:",
        ctaButtonText: {
            "Back": "Transfer & Optimize Design to Backside",
            "Front (Pocket)": "Apply & Optimize Design to Frontside (Pocket)",
            "Back & Pocket": "Apply & Optimize Design to Both Sides"
        },
        cardId: "OVERSIZED_TSHIRT-card",
        editButtonClass: "OVERSIZED_TSHIRT-edit-btn",
        resizeContainerClass: "oversized-resize-container",
        uploadInputSelector: {
          back: 'label.file-upload-input[for="OVERSIZED_TSHIRT-BACK-wizzy"]',
          front: 'label.file-upload-input[for="OVERSIZED_TSHIRT-FRONT-wizzy"]'
        },
        tabs: ["Back", "Front (Pocket)", "Back & Pocket"],
        FRONTCanvasDimensions: { width: 4500, height: 5400 },
        BACKCanvasDimensions: { width: 4500, height: 5400 },
        sidestabSelector: {
          back: "btn btn-outline-primary btn-BACK.ng-star-inserted",
          front: "btn btn-outline-primary btn-FRONT.ng-star-inserted"
        },
        injectPosition: "second"
      }
    ];
  
    function updatePreferences() {
      localStorage.setItem("clothingResizePreferences", JSON.stringify(preferences));
    }
  
    function delay(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms));
    }
  
    function updateTabStyles(product, tabContainer) {
      const prefs = preferences[product.name];
      const tabButtons = tabContainer.querySelectorAll(".tab-button");
      
      tabButtons.forEach((button) => {
        if (button.dataset.tabName === prefs.selectedTab) {
          button.classList.add("active");
        } else {
          button.classList.remove("active");
        }
      });
    }
  
    function injectResizeContainer(product, targetElement) {
      if (processingFlags[product.name]) return;
      if (targetElement.querySelector(`.${product.resizeContainerClass}`)) {
        processingFlags[product.name] = false;
        return;
      }
      processingFlags[product.name] = true;

      products.forEach((p) => {
        if (p.name !== product.name) {
          const existingContainer = targetElement.querySelector(`.${p.resizeContainerClass}`);
          if (existingContainer) {
            existingContainer.remove();
          }
        }
      });

      const container = (function (prod) {
        const div = document.createElement("div");
        div.className = prod.resizeContainerClass;
        div.style.backgroundColor = "#ffffff";
        div.style.border = "0.0625rem solid #d5dbdb";
        div.style.marginTop = "10px";
        div.style.borderRadius = "0px";
        div.style.boxSizing = "border-box";
        div.style.fontFamily = '"Amazon Ember", sans-serif';
        div.style.padding = "15px";
  
        const header = document.createElement("div");
        header.textContent = prod.containerHeading;
        header.style.fontSize = "13px";
        header.style.fontWeight = "600";
        header.style.color = "#333333";
        header.style.backgroundColor = "#fafafa";
        header.style.borderBottom = "0.0625rem solid #efefef";
        header.style.padding = "7.5px 15px";
        header.style.margin = "-15px -15px 15px -15px";
        header.style.width = "auto";
        div.appendChild(header);
  
        const infoContainer = document.createElement("div");
        infoContainer.style.marginBottom = "10px";
        infoContainer.style.display = "flex";
        infoContainer.style.alignItems = "center";
        infoContainer.style.fontSize = "12px";
        infoContainer.style.color = "#666";
        
        const infoIcon = document.createElement("img");
        infoIcon.src = chrome.runtime.getURL("assets/tip-ic.svg");
        infoIcon.style.width = "12px";
        infoIcon.style.height = "12px";
        infoIcon.style.marginRight = "5px";
        infoIcon.style.cursor = "help";
        
        const infoText = document.createElement("span");
        infoText.textContent = "Designs are automatically compressed and optimized for quality";
        
        infoContainer.appendChild(infoIcon);
        infoContainer.appendChild(infoText);
        div.appendChild(infoContainer);
  
        if (prod.tabs.length > 1) {
          if (!preferences[prod.name]) {
            preferences[prod.name] = {
              selectedTab: prod.tabs[0],
              tabs: {},
              downloadaCopy: false
            };
            updatePreferences();
          }

          const tabContainer = document.createElement("div");
          tabContainer.className = "tab-container";

          prod.tabs.forEach((tab) => {
            const tabButton = document.createElement("button");
            tabButton.textContent = tab;
            tabButton.className = "tab-button";
            tabButton.dataset.tabName = tab;

            if (preferences[prod.name].selectedTab === tab) {
              tabButton.classList.add("active");
            }

            tabButton.addEventListener("click", async function () {
              const prefs = preferences[prod.name];
              if (prefs.selectedTab === tab) return;
              
              prefs.selectedTab = tab;
              updatePreferences();
              
              updateTabStyles(prod, tabContainer);
              const btnText = prod.ctaButtonText[tab] || prod.ctaButtonText["Back"];
              const applyBtn = div.querySelector(".resize-btn span");
              if (applyBtn) applyBtn.textContent = btnText;
              await delay(300);
            });

            tabContainer.appendChild(tabButton);
          });

          div.appendChild(tabContainer);
        }

        const applyContainer = document.createElement("div");
        applyContainer.className = "apply-btn-container";
        applyContainer.style.textAlign = "left";
        applyContainer.style.marginTop = "10px";
        applyContainer.style.marginBottom = "0px";
        const applyBtn = document.createElement("button");
        applyBtn.type = "button";
        applyBtn.className = "resize-btn";
        applyBtn.disabled = true;
        applyBtn.style.cursor = "not-allowed";
        applyBtn.style.backgroundColor = "#cfd4d4";
        applyBtn.style.color = "rgba(0, 0, 0, 0.5)";
        applyBtn.title = "Applies the design with automatic compression and DPI optimization for optimal quality.";
        const btnIcon = document.createElement("img");
        btnIcon.src = chrome.runtime.getURL("assets/apply.svg");
        btnIcon.alt = "Apply and Upload Icon";
        btnIcon.style.width = "20px";
        btnIcon.style.height = "20px";
        btnIcon.style.marginRight = "10px";
        btnIcon.style.filter = "brightness(0)";
        btnIcon.style.opacity = "0.5";
        const btnText = document.createElement("span");

        const selectedTab = preferences[prod.name]?.selectedTab || prod.tabs[0];
        btnText.textContent = prod.ctaButtonText[selectedTab] || prod.ctaButtonText["Back"];

        applyBtn.appendChild(btnIcon);
        applyBtn.appendChild(btnText);

        applyBtn.addEventListener("click", async function() {
          if (applyBtn.disabled) return;
          try {
            applyBtn.disabled = true;
            applyBtn.style.cursor = "not-allowed";
            applyBtn.style.backgroundColor = "#cfd4d4";
            applyBtn.style.color = "rgba(0, 0, 0, 0.5)";
            btnIcon.style.filter = "brightness(0)";
            btnIcon.style.opacity = "0.5";

            const selectedTab = preferences[prod.name].selectedTab;
            await handleUpload(prod, selectedTab);

            const deleteButton = div.closest(".product-editor")?.querySelector(".delete-button");
            if (deleteButton) {
              applyBtn.disabled = false;
              applyBtn.style.cursor = "pointer";
              applyBtn.style.backgroundColor = "#470CED";
              applyBtn.style.color = "#FFFFFF";
              btnIcon.style.filter = "brightness(0) invert(1)";
              btnIcon.style.opacity = "1";
            }
          } catch (error) {
            console.error();
            applyBtn.disabled = false;
            applyBtn.style.cursor = "pointer";
            applyBtn.style.backgroundColor = "#470CED";
            applyBtn.style.color = "#FFFFFF";
            btnIcon.style.filter = "brightness(0) invert(1)";
            btnIcon.style.opacity = "1";
          }
        });

        applyContainer.appendChild(applyBtn);
        div.appendChild(applyContainer);

        return div;
      })(product);
  
      if (product.injectPosition === "second" && targetElement.children.length >= 1) {
        targetElement.insertBefore(container, targetElement.children[1]);
      } else {
        targetElement.appendChild(container);
      }
      observeButtonState(product, container);
      processingFlags[product.name] = false;
    }
  
    function observeButtonState(product, container) {
      if (!container.querySelector(".resize-btn")) return;
      updateButtonState(product, container);
      const editor = container.closest(".product-editor");
      if (!editor) return;
      const config = { childList: true, subtree: true };
      new MutationObserver(() => {
        updateButtonState(product, container);
      }).observe(editor, config);
    }
  
    function updateButtonState(product, container) {
      if (!container) return;
      const applyBtn = container.querySelector(".resize-btn");
      if (!applyBtn) return;
      let hasDelete = false;
      const editor = container.closest(".product-editor");
      if (editor) {
        hasDelete = !!editor.querySelector(".delete-button");
      }
      applyBtn.disabled = !hasDelete;
      applyBtn.style.cursor = hasDelete ? "pointer" : "not-allowed";
      if (applyBtn.disabled) {
        applyBtn.style.backgroundColor = "#cfd4d4";
        applyBtn.style.color = "rgba(0, 0, 0, 0.5)";
        const img = applyBtn.querySelector("img");
        if (img) {
          img.style.filter = "brightness(0)";
          img.style.opacity = "0.5";
        }
      } else {
        applyBtn.style.backgroundColor = "#470CED";
        applyBtn.style.color = "#FFFFFF";
        const img = applyBtn.querySelector("img");
        if (img) {
          img.style.filter = "brightness(0) invert(1)";
          img.style.opacity = "1";
        }
      }
    }

    const blockedClasses = [
      "POP_SOCKET-edit-btn",
      "PHONE_CASE_APPLE_IPHONE-edit-btn",
      "TOTE_BAG-edit-btn"
    ];

    document.addEventListener("click", function (e) {
      if (
        e.target.classList &&
        blockedClasses.some(cls => e.target.classList.contains(cls))
      ) {
        const productEditor = e.target.closest("div.mb-base");
        if (productEditor) {
          const zipContainer = productEditor.querySelector(".zip-resize-container");
          if (zipContainer) {
            zipContainer.remove();
          }
        }
        return;
      }

      if (
        e.target.matches(".btn.btn-secondary.btn-edit") ||
        e.target.matches(".btn-secondary.btn-edit")
      ) {
        const btn = e.target;
        const classes = Array.from(btn.classList);
        const productName = classes
          .find((c) => c.endsWith("-edit-btn"))
          ?.replace("-edit-btn", "")
          ?.toUpperCase();
        if (!productName) return;
        const product = products.find((p) => p.name === productName);
        if (!product) return;

        if (blockedClasses.some((cls) => btn.classList.contains(cls))) {
          const productEditor = btn.closest("div.mb-base");
          if (productEditor) {
            const zipContainer = productEditor.querySelector(".zip-resize-container");
            if (zipContainer) zipContainer.remove();
          }
          return;
        }

        const baseContainer = btn.closest("div.mb-base");
        if (!baseContainer) return;
        const editorCol = baseContainer.querySelector(".form-row > .col-6:nth-child(2)");
        if (!editorCol) return;
        injectResizeContainer(product, editorCol);
      }
    });
  
    const container = document.querySelector(".product-editor");
    if (container) {
      new MutationObserver(function () {
        container.querySelectorAll("div.mb-base").forEach((base) => {
          products.forEach((prod) => {
            const editorCol = base.querySelector(".form-row > .col-6:nth-child(2)");
            if (
              editorCol &&
              !base.querySelector(`.${prod.resizeContainerClass}`)
            ) {
              const editButton = base.querySelector(".btn-secondary.btn-edit");
              if (editButton) {
                if (
                  blockedClasses.some((cls) =>
                    editButton.classList.contains(cls)
                  )
                ) {
                  const existingZip = base.querySelector(".zip-resize-container");
                  if (existingZip) existingZip.remove();
                  return;
                }
              }
              injectResizeContainer(prod, editorCol);
            }
          });
        });
      }).observe(container, { childList: true, subtree: true });
    }
})();
