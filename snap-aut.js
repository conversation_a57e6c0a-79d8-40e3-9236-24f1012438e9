
async function retryWithBackoff(fn, { retries = 3, base = 300, max = 2000, factor = 2 } = {}) {
  for (let attempt = 0; attempt <= retries; attempt++) {
    try { return await fn(); }
    catch (e) { if (attempt === retries) throw e; const delay = Math.min(max, base * Math.pow(factor, attempt)); await new Promise(r => setTimeout(r, delay)); }
  }
}



class SnapAutomation {
    constructor() {
        this.isProcessing = false;
        this.currentFile = null;
        this.settings = null;
        this.retryCount = 0;
        this.maxRetries = 5;
        this.currentOperation = null;
        this.uploadComplete = false;
        this.generatingImageCount = 0;
        this.requiredGeneratingImageCount = 1;
        this.lastClickedProduct = null;
        this.productSelectors = {
            uploadContainer: '.snap-upload-container',
            deleteIcon: '.sci-icon.sci-delete-forever',
            generatingMessages: [
                "Generating image....",
                "Bild lädt....",
                "Chargement de l'image....",
                "Creando immagine....",
                "Generando imagen....",
                "画像を読み込み中...."
            ],
            textFields: [
                '#designCreator-productEditor-title',
                '#designCreator-productEditor-brandName',
                '#designCreator-productEditor-featureBullet1',
                '#designCreator-productEditor-featureBullet2',
                '#designCreator-productEditor-description'
            ],
            copyEnToAll: {
                checkmark: '.col-auto.pl-medium-large.pr-0.pt-1.ml-3.mb-0',
                container: '.ng-star-inserted',
                findContainer: 'snap-copy-find-container',
                copyBtn: '#copyBtn',
                pasteAllBtn: '#pasteAllBtn'
            },
            saveToDrafts: 'button#draft-button'
        };

        this.initialize();
    }

    async initialize() {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            const snapParam = urlParams.get('Snap');

            if (!snapParam) {
                return;
            }

            this.hideSnapBulkUploadButton();

            const [index, ...filenameParts] = snapParam.split('-');
            const filename = filenameParts.join('-');

            if (!filename) {
                return;
            }

            const state = await this.getAutomationState();
            if (!state || !state.isRunning) {

                return;
            }

            this.currentFile = state.currentFile;
            this.settings = state.settings;






            await new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    action: 'setActiveProgressTab'
                }, (response) => {
                    if (response && response.success) {

                    } else {

                    }
                    resolve();
                });
            });

            if (this.settings) {


                if (this.settings.clothingProducts) {
                    const isActive = this.settings.clothingProducts.isActive === true;


                    this.settings.clothingProducts.isActive = isActive;
                } else {

                    this.settings.clothingProducts = { isActive: false };
                }

                if (this.settings.scalableProducts) {
                    const isActive = this.settings.scalableProducts.isActive === true;


                    this.settings.scalableProducts.isActive = isActive;
                } else {

                    this.settings.scalableProducts = { isActive: false };
                }

                if (this.settings.tumblerProduct) {
                    const isActive = this.settings.tumblerProduct.isActive === true;


                    this.settings.tumblerProduct.isActive = isActive;
                } else {

                    this.settings.tumblerProduct = { isActive: false };
                }

                if (this.settings.mugProducts) {
                    const isActive = this.settings.mugProducts.isActive === true;


                    this.settings.mugProducts.isActive = isActive;
                } else {

                    this.settings.mugProducts = { isActive: false };
                }


                const activeProducts = [];
                if (this.settings.clothingProducts.isActive === true) activeProducts.push('Clothing');
                if (this.settings.scalableProducts.isActive === true) activeProducts.push('Scalable');
                if (this.settings.tumblerProduct.isActive === true) activeProducts.push('Tumbler');
                if (this.settings.mugProducts?.isActive === true) activeProducts.push('Mug');


            } else {

                this.settings = {
                    clothingProducts: { isActive: false },
                    scalableProducts: { isActive: false },
                    tumblerProduct: { isActive: false },
                    mugProducts: { isActive: false }
                };
            }

            if (!this.currentFile || (!this.currentFile.blobUrl && !this.currentFile.base64)) {

                return;
            }

            this.updateProgressUI('Uploading', false);

            this.startAutomation();

        } catch (error) {

            this.handleError(error);
        }
    }

    async startAutomation() {
        try {
            if (this.isProcessing) {

                return;
            }

            this.isProcessing = true;


            this.updateProgressUI('Uploading', false);

            if (!this.settings) {

                throw new Error();
            }

            const activeProducts = [];
            if (this.settings.clothingProducts?.isActive === true) activeProducts.push('Clothing');
            if (this.settings.scalableProducts?.isActive === true) activeProducts.push('Scalable');
            if (this.settings.tumblerProduct?.isActive === true) activeProducts.push('Tumbler');
            if (this.settings.mugProducts?.isActive === true) activeProducts.push('Mug');




            await this.waitForElement(this.productSelectors.uploadContainer);


            await this.uploadFile();


            await this.waitForElement(this.productSelectors.deleteIcon);
            this.uploadComplete = true;



            await this.sleep(500);

            this.updateProgressUI('Processing Products', false);

            await this.processProducts();


            await this.sleep(500);

            await this.processActions();


            await this.sleep(500);


            this.updateProgressUI('Completed', true);

            this.isProcessing = false;

            await this.processNextFile();

        } catch (error) {

            this.handleError(error);
        }
    }

    async uploadFile() {
        try {

            this.currentOperation = 'Uploading';

            this.updateProgressUI('Uploading', false);

            const useNativeUploader = this.settings && this.settings.useNativeUploader === true;



            if (useNativeUploader) {

                const dropzoneContainer = document.querySelector('div.dropzone-container');
                if (!dropzoneContainer) {
                    throw new Error('');
                }
                const fileInput = dropzoneContainer.querySelector('input[type="file"]');
                if (!fileInput) {
                    throw new Error('');
                }
                let file;
                if (this.currentFile.blobUrl) {
                    file = await this.blobUrlToFile(this.currentFile.blobUrl, this.currentFile.filename);
                } else if (this.currentFile.base64) {
                    file = this.base64ToFile(this.currentFile.base64, this.currentFile.filename);
                } else {
                    throw new Error('');
                }
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                fileInput.files = dataTransfer.files;
                const changeEvent = new Event('change', { bubbles: true });
                fileInput.dispatchEvent(changeEvent);
            } else {

                const uploadContainer = document.querySelector(this.productSelectors.uploadContainer);
                if (!uploadContainer) {
                    throw new Error('');
                }
                let file;
                if (this.currentFile.blobUrl) {
                    file = await this.blobUrlToFile(this.currentFile.blobUrl, this.currentFile.filename);
                } else if (this.currentFile.base64) {
                    file = this.base64ToFile(this.currentFile.base64, this.currentFile.filename);
                } else {
                    throw new Error('');
                }
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                const dropEvent = new DragEvent('drop', {
                    bubbles: true,
                    cancelable: true,
                    dataTransfer
                });
                uploadContainer.dispatchEvent(dropEvent);
            }


            await this.sleep(500);

        } catch (error) {

            throw error;
        }
    }

    async processProducts() {
        try {


            if (!this.settings) {

                throw new Error();
            }



            if (this.settings.clothingProducts && this.settings.clothingProducts.isActive === true) {


                this.updateProgressUI('Processing Products', false);
                await this.processClothingProducts();


                await this.sleep(1000);
            } else {

            }

            if (this.settings.scalableProducts && this.settings.scalableProducts.isActive === true) {


                this.updateProgressUI('Processing Scalable Products', false);
                await this.processScalableProducts();


                await this.sleep(1000);
            } else {

            }

            if (this.settings.tumblerProduct && this.settings.tumblerProduct.isActive === true) {


                this.updateProgressUI('Processing Tumbler', false);
                await this.processTumblerProduct();


                await this.sleep(1000);
            } else {

            }

            if (this.settings.mugProducts && this.settings.mugProducts.isActive === true) {


                this.updateProgressUI(`Processing Ceramic Mug`, false);
                await this.processMugProduct();

                await this.sleep(1000);
            } else {

            }

        } catch (error) {

            throw error;
        }
    }

    async processActions() {
        try {


            const actions = this.settings?.actions;
            if (!actions) {

                return;
            }

            if (actions.textSwap) {


                this.updateProgressUI('Text Swap', false);
                await this.processTextSwap();


                await this.sleep(1000);
            } else {

            }

            if (actions.copyEnToAll) {


                this.updateProgressUI('Copy EN to All', false);
                await this.processCopyEnToAll();


                await this.sleep(1000);
            } else {

            }

            if (actions.saveToDrafts) {


                this.updateProgressUI('Save to Drafts', false);
                await this.processSaveToDrafts();


                await this.sleep(1000);
            } else {

            }

        } catch (error) {

            throw error;
        }
    }

    async processTextSwap() {
        try {

            this.updateProgressUI('Text Swap', false);

            const cleanedFilename = this.cleanFilenameForTextSwap(this.currentFile.filename);


            for (const selector of this.productSelectors.textFields) {
                const field = document.querySelector(selector);
                if (field) {

                    const originalText = field.value;
                    const newText = originalText.replace(/#snap/g, cleanedFilename);

                    if (originalText !== newText) {

                        field.value = newText;
                        field.dispatchEvent(new Event('input', { bubbles: true }));

            
                        await this.sleep(250);
                    } else {
            
                    }
                } else {
        
                }
            }


            await this.sleep(400);



        } catch (error) {

            throw error;
        }
    }

    async processCopyEnToAll() {
        try {

            this.updateProgressUI('Copy EN to All', false);


            const checkmark = document.querySelector(this.productSelectors.copyEnToAll.checkmark);
            if (checkmark) {

                checkmark.click();


                await this.sleep(300);


                const shadowHostElement = document.querySelector('snap-copy-find-container');

                if (shadowHostElement) {


                    const shadowRoot = shadowHostElement.shadowRoot;

                    if (shadowRoot) {



                        const copyBtn = shadowRoot.querySelector('#copyBtn');
                        if (copyBtn) {

                            copyBtn.click();


                            await this.sleep(300);


                            const pasteAllBtn = shadowRoot.querySelector('#pasteAllBtn');
                            if (pasteAllBtn) {

                                pasteAllBtn.click();


                                await this.sleep(1500);
                            } else {

                            }
                        } else {

                        }
                    } else {


                        this.tryFallbackCopyPasteMethod();
                    }
                } else {


                    this.tryFallbackCopyPasteMethod();
                }
            } else {

            }



        } catch (error) {

            throw error;
        }
    }

    async tryFallbackCopyPasteMethod() {


        const allElements = document.querySelectorAll('*');
        let foundShadowRoot = false;

        for (const element of allElements) {
            if (element.shadowRoot) {

                const shadowRoot = element.shadowRoot;

                const copyBtn = shadowRoot.querySelector('#copyBtn');
                if (copyBtn) {

                    copyBtn.click();
                    await this.sleep(300);

                    const pasteAllBtn = shadowRoot.querySelector('#pasteAllBtn');
                    if (pasteAllBtn) {

                        pasteAllBtn.click();
                        await this.sleep(1500);
                        foundShadowRoot = true;
                        break;
                    }
                }
            }
        }

        if (!foundShadowRoot) {


            const container = document.querySelector(this.productSelectors.copyEnToAll.container);
            const copyContainer = container?.querySelector(this.productSelectors.copyEnToAll.findContainer);

            if (copyContainer) {


                const copyBtn = copyContainer.querySelector(this.productSelectors.copyEnToAll.copyBtn);
                if (copyBtn) {

                    copyBtn.click();


                    await this.sleep(300);


                    const pasteAllBtn = copyContainer.querySelector(this.productSelectors.copyEnToAll.pasteAllBtn);
                    if (pasteAllBtn) {

                        pasteAllBtn.click();


                        await this.sleep(1500);
                    } else {

                    }
                } else {

                }
            } else {

            }
        }
    }

    async processSaveToDrafts() {
        try {

            this.updateProgressUI('Save to Drafts', false);


            const draftButton = document.querySelector(this.productSelectors.saveToDrafts);
            if (draftButton) {

                draftButton.click();


                await this.sleep(1000);


            } else {

            }

        } catch (error) {

            throw error;
        }
    }

    async processClothingProducts() {
        try {


            const settings = this.settings.clothingProducts;

            if (!settings || settings.isActive !== true) {

                return;
            }

            const shirtProductTypes = [
                'STANDARD_TSHIRT',
                'PREMIUM_TSHIRT',
                'OVERSIZED_TSHIRT',
                'VNECK',
                'TANK_TOP',
                'STANDARD_LONG_SLEEVE',
                'RAGLAN',
                'STANDARD_SWEATSHIRT'
            ];

            const allClothingProductTypes = [
                ...shirtProductTypes,
                'STANDARD_PULLOVER_HOODIE',
                'ZIP_HOODIE'
            ];

            const availableProducts = {};
            const disabledProducts = new Set();

            for (const productType of allClothingProductTypes) {
                const editButtonSelector = `.${productType}-edit-btn`;
                const editButton = document.querySelector(editButtonSelector);

                if (editButton) {

                    const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                    const productCard = editButton.closest('.product-card');
                    const isCardDisabled = productCard && productCard.classList.contains('disabled');

                    if (isButtonDisabled || isCardDisabled) {

                        disabledProducts.add(productType);
                    } else {
                        availableProducts[productType] = editButton;

                    }
                } else {

                }
            }



            const hasGlobalColorSettings = settings.allProducts && settings.allProducts.colors !== 'Skip';
            const hasGlobalPriceSettings = settings.allProducts && settings.allProducts.prices !== 'Skip';

            const processedProducts = new Set();

            const hasShirtNonDefaultSettings = settings.shirts && (
                settings.shirts.sidesOptions !== 'Default (Front)' ||
                (settings.shirts.colors && settings.shirts.colors !== 'Skip') ||
                (settings.shirts.prices && settings.shirts.prices !== 'Skip')
            );

            if (hasShirtNonDefaultSettings) {


                let processedCount = 0;
                const foundShirts = Object.keys(availableProducts).filter(type => shirtProductTypes.includes(type));



                for (const shirtType of foundShirts) {
                    try {


                        const editButton = availableProducts[shirtType];

                        const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                        const productCard = editButton.closest('.product-card');
                        const isCardDisabled = productCard && productCard.classList.contains('disabled');

                        if (isButtonDisabled || isCardDisabled) {

                            continue;
                        }



                        this.updateProgressUI(`Processing ${shirtType}`, false);

                        editButton.click();


                        await this.sleep(1200);

                        if (settings.shirts.colors && settings.shirts.colors !== 'Skip') {

                            await this.processProductColors(settings.shirts.colors);


                            await this.sleep(250);
                        } else if (hasGlobalColorSettings) {


                            await this.processProductColors(settings.allProducts.colors);


                            await this.sleep(250);
                        }

                        if (settings.shirts.prices && settings.shirts.prices !== 'Skip') {

                            await this.processProductPrices(settings.shirts.prices);


                            await this.sleep(250);
                        } else if (hasGlobalPriceSettings) {


                            await this.processProductPrices(settings.allProducts.prices);


                            await this.sleep(250);
                        }

                        if (settings.shirts.sidesOptions !== 'Default (Front)') {

                            await this.processProductSides(shirtType, settings.shirts.sidesOptions);


                            await this.sleep(500);
                        }

                        processedProducts.add(shirtType);
                        processedCount++;

                        if (processedCount < foundShirts.length) {

                            await this.sleep(1000);
                        }
                    } catch (error) {

                    }
                }


            } else if (hasGlobalColorSettings || hasGlobalPriceSettings) {



                const foundShirts = Object.keys(availableProducts).filter(type => shirtProductTypes.includes(type));

                for (let i = 0; i < foundShirts.length; i++) {
                    const shirtType = foundShirts[i];
                    try {


                        const editButton = availableProducts[shirtType];

                        const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                        const productCard = editButton.closest('.product-card');
                        const isCardDisabled = productCard && productCard.classList.contains('disabled');

                        if (isButtonDisabled || isCardDisabled) {

                            continue;
                        }



                        this.updateProgressUI(`Processing ${shirtType}`, false);

                        editButton.click();


                        await this.sleep(1200);

                        if (hasGlobalColorSettings) {

                            await this.processProductColors(settings.allProducts.colors);


                            await this.sleep(250);
                        }

                        if (hasGlobalPriceSettings) {

                            await this.processProductPrices(settings.allProducts.prices);


                            await this.sleep(250);
                        }

                        processedProducts.add(shirtType);

                        if (i < foundShirts.length - 1) {

                            await this.sleep(1000);
                        }
                    } catch (error) {


                    }
                }
            } else {

            }

            const hasPulloverHoodieNonDefaultSettings = settings.pulloverHoodie && (
                settings.pulloverHoodie.sidesOptions !== 'Default (Front)' ||
                (settings.pulloverHoodie.colors && settings.pulloverHoodie.colors !== 'Skip') ||
                (settings.pulloverHoodie.prices && settings.pulloverHoodie.prices !== 'Skip')
            );

            if (hasPulloverHoodieNonDefaultSettings && availableProducts['STANDARD_PULLOVER_HOODIE'] && !processedProducts.has('STANDARD_PULLOVER_HOODIE')) {
                try {


                    const editButton = availableProducts['STANDARD_PULLOVER_HOODIE'];

                    const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                    const productCard = editButton.closest('.product-card');
                    const isCardDisabled = productCard && productCard.classList.contains('disabled');

                    if (isButtonDisabled || isCardDisabled) {

                    } else {



                        this.updateProgressUI('Processing Pullover Hoodie', false);

                        editButton.click();


                        await this.sleep(1200);

                        if (settings.pulloverHoodie.colors && settings.pulloverHoodie.colors !== 'Skip') {

                            await this.processProductColors(settings.pulloverHoodie.colors);


                            await this.sleep(250);
                        } else if (hasGlobalColorSettings) {


                            await this.processProductColors(settings.allProducts.colors);


                            await this.sleep(250);
                        }

                        if (settings.pulloverHoodie.prices && settings.pulloverHoodie.prices !== 'Skip') {

                            await this.processProductPrices(settings.pulloverHoodie.prices);


                            await this.sleep(250);
                        } else if (hasGlobalPriceSettings) {


                            await this.processProductPrices(settings.allProducts.prices);


                            await this.sleep(250);
                        }

                        if (settings.pulloverHoodie.sidesOptions !== 'Default (Front)') {

                            await this.processProductSides('STANDARD_PULLOVER_HOODIE', settings.pulloverHoodie.sidesOptions);


                            await this.sleep(500);
                        }

                        processedProducts.add('STANDARD_PULLOVER_HOODIE');
                    }
                } catch (error) {

                }
            } else if (availableProducts['STANDARD_PULLOVER_HOODIE'] && !processedProducts.has('STANDARD_PULLOVER_HOODIE') && (hasGlobalColorSettings || hasGlobalPriceSettings)) {

                try {


                    const editButton = availableProducts['STANDARD_PULLOVER_HOODIE'];

                    const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                    const productCard = editButton.closest('.product-card');
                    const isCardDisabled = productCard && productCard.classList.contains('disabled');

                    if (isButtonDisabled || isCardDisabled) {

                    } else {



                        this.updateProgressUI('Processing Pullover Hoodie', false);

                        editButton.click();


                        await this.sleep(1200);

                        if (hasGlobalColorSettings) {

                            await this.processProductColors(settings.allProducts.colors);


                            await this.sleep(250);
                        }

                        if (hasGlobalPriceSettings) {

                            await this.processProductPrices(settings.allProducts.prices);


                            await this.sleep(250);
                        }

                        processedProducts.add('STANDARD_PULLOVER_HOODIE');
                    }
                } catch (error) {

                }
            } else if (disabledProducts.has('STANDARD_PULLOVER_HOODIE')) {

            } else if (availableProducts['STANDARD_PULLOVER_HOODIE'] && !processedProducts.has('STANDARD_PULLOVER_HOODIE')) {

            } else if (processedProducts.has('STANDARD_PULLOVER_HOODIE')) {

            } else {

            }

            const hasZipHoodieNonDefaultSettings = settings.zipHoodie && (
                settings.zipHoodie.sidesOptions !== 'Default (Front)' ||
                (settings.zipHoodie.colors && settings.zipHoodie.colors !== 'Skip') ||
                (settings.zipHoodie.prices && settings.zipHoodie.prices !== 'Skip')
            );

            if (hasZipHoodieNonDefaultSettings && availableProducts['ZIP_HOODIE'] && !processedProducts.has('ZIP_HOODIE')) {
                try {


                    const editButton = availableProducts['ZIP_HOODIE'];

                    const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                    const productCard = editButton.closest('.product-card');
                    const isCardDisabled = productCard && productCard.classList.contains('disabled');

                    if (isButtonDisabled || isCardDisabled) {

                    } else {



                        this.updateProgressUI('Processing Zip Hoodie', false);

                        editButton.click();


                        await this.sleep(1200);

                        if (settings.zipHoodie.colors && settings.zipHoodie.colors !== 'Skip') {

                            await this.processProductColors(settings.zipHoodie.colors);


                            await this.sleep(250);
                        } else if (hasGlobalColorSettings) {


                            await this.processProductColors(settings.allProducts.colors);


                            await this.sleep(250);
                        }

                        if (settings.zipHoodie.prices && settings.zipHoodie.prices !== 'Skip') {

                            await this.processProductPrices(settings.zipHoodie.prices);


                            await this.sleep(250);
                        } else if (hasGlobalPriceSettings) {


                            await this.processProductPrices(settings.allProducts.prices);


                            await this.sleep(250);
                        }

                        if (settings.zipHoodie.sidesOptions !== 'Default (Front)') {

                            await this.processProductSides('ZIP_HOODIE', settings.zipHoodie.sidesOptions);


                            await this.sleep(500);
                        }

                        processedProducts.add('ZIP_HOODIE');
                    }
                } catch (error) {

                }
            } else if (availableProducts['ZIP_HOODIE'] && !processedProducts.has('ZIP_HOODIE') && (hasGlobalColorSettings || hasGlobalPriceSettings)) {

                try {


                    const editButton = availableProducts['ZIP_HOODIE'];

                    const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                    const productCard = editButton.closest('.product-card');
                    const isCardDisabled = productCard && productCard.classList.contains('disabled');

                    if (isButtonDisabled || isCardDisabled) {

                    } else {



                        this.updateProgressUI('Processing Zip Hoodie', false);

                        editButton.click();


                        await this.sleep(1200);

                        if (hasGlobalColorSettings) {

                            await this.processProductColors(settings.allProducts.colors);


                            await this.sleep(250);
                        }

                        if (hasGlobalPriceSettings) {

                            await this.processProductPrices(settings.allProducts.prices);


                            await this.sleep(250);
                        }

                        processedProducts.add('ZIP_HOODIE');
                    }
                } catch (error) {

                }
            } else if (disabledProducts.has('ZIP_HOODIE')) {

            } else if (availableProducts['ZIP_HOODIE'] && !processedProducts.has('ZIP_HOODIE')) {

            } else if (processedProducts.has('ZIP_HOODIE')) {

            } else {

            }




        } catch (error) {


        }
    }

    async processProductSides(productName, sidesOption) {
        try {

            this.updateProgressUI(`Processing ${productName}`, false);

            let tabButton;
            let isBackAndPocket = false;

            if (sidesOption === 'Back') {
                tabButton = Array.from(document.querySelectorAll('.tab-button')).find(
                    btn => btn.textContent.trim() === 'Back'
                );

            } else if (sidesOption === 'Frontside (Pocket)') {
                tabButton = Array.from(document.querySelectorAll('.tab-button')).find(
                    btn => btn.textContent.trim().includes('Pocket')
                );

            } else if (sidesOption === 'Back & Pocket') {
                tabButton = Array.from(document.querySelectorAll('.tab-button')).find(
                    btn => btn.textContent.trim().includes('Back & Pocket')
                );

                isBackAndPocket = true;
            }

            if (!tabButton) {

                return;
            }

            if (tabButton.classList.contains('active')) {

            } else {


                tabButton.click();


                await this.sleep(500);
            }


            const resizeBtn = await this.waitForElement('.resize-btn:not([disabled])');


            await retryWithBackoff(() => { resizeBtn.click(); return Promise.resolve(); });

            if (isBackAndPocket) {


                await this.waitForBackAndPocketMessages();
            } else {


                await this.sleep(1500);


                await this.waitForAnyGeneratingMessage();


                await this.sleep(150);
            }



        } catch (error) {


        }
    }

    async processProductColors(colorOption) {
        try {


            this.currentOperation = 'processProductColors';

            const isEditorOpen = document.querySelector('.product-editor') !== null;


            if (!isEditorOpen) {


                const productTypes = [
                    'STANDARD_TSHIRT',
                    'PREMIUM_TSHIRT',
                    'VNECK',
                    'TANK_TOP',
                    'STANDARD_LONG_SLEEVE',
                    'RAGLAN',
                    'STANDARD_SWEATSHIRT',
                    'STANDARD_PULLOVER_HOODIE',
                    'ZIP_HOODIE'
                ];

                let editButton = null;
                let productToEdit = null;

                for (const productType of productTypes) {
                    const button = document.querySelector(`.${productType}-edit-btn`);
                    if (button && !button.disabled && !button.classList.contains('disabled')) {

                        const productCard = button.closest('.product-card');
                        const isCardDisabled = productCard && productCard.classList.contains('disabled');

                        if (!isCardDisabled) {
                            editButton = button;
                            productToEdit = productType;
                            break;
                        }
                    }
                }

                if (editButton) {


                    if (productToEdit === 'STANDARD_TSHIRT') {
                        this.lastClickedProduct = 'Standard T-shirt';
                    } else if (productToEdit === 'PREMIUM_TSHIRT') {
                        this.lastClickedProduct = 'Premium T-shirt';
                    } else if (productToEdit === 'OVERSIZED_TSHIRT') {
                        this.lastClickedProduct = 'Comfort Colors Heavyweight T-shirt';
                    } else if (productToEdit === 'VNECK') {
                        this.lastClickedProduct = 'V-neck';
                    } else if (productToEdit === 'TANK_TOP') {
                        this.lastClickedProduct = 'Tank Top';
                    } else if (productToEdit === 'STANDARD_LONG_SLEEVE') {
                        this.lastClickedProduct = 'Long Sleeve';
                    } else if (productToEdit === 'RAGLAN') {
                        this.lastClickedProduct = 'Raglan';
                    } else if (productToEdit === 'STANDARD_SWEATSHIRT') {
                        this.lastClickedProduct = 'Sweatshirt';
                    } else if (productToEdit === 'STANDARD_PULLOVER_HOODIE') {
                        this.lastClickedProduct = 'Pullover Hoodie';
                    } else if (productToEdit === 'ZIP_HOODIE') {
                        this.lastClickedProduct = 'Zip Hoodie';
                    }

                    this.updateProgressUI(`Processing ${productToEdit}`, false);


                    editButton.click();


                    await this.sleep(1200);
                } else {

                    return;
                }
            } else {

            }


            const colorContainer = await this.waitForElement('.color-groups-container');

            if (!colorContainer) {

                return;
            }



            const colorActionsContainer = document.querySelector('.product-color-actions');
            if (!colorActionsContainer) {

                return;
            }

            let colorButton;

            if (colorOption === 'Dark Colors') {
                colorButton = document.querySelector('#product-dark-colors-btn');

            } else if (colorOption === 'Light Colors') {
                colorButton = document.querySelector('#product-light-colors-btn');

            } else if (colorOption === 'All Colors') {
                colorButton = document.querySelector('#product-all-colors-btn');

            }

            if (!colorButton) {


                if (colorOption === 'Dark Colors') {
                    colorButton = document.querySelector('button[id="product-dark-colors-btn"]');

                } else if (colorOption === 'Light Colors') {
                    colorButton = document.querySelector('button[id="product-light-colors-btn"]');

                } else if (colorOption === 'All Colors') {
                    colorButton = document.querySelector('button[id="product-all-colors-btn"]');

                }

                if (!colorButton) {

                    return;
                }
            }


            colorButton.click();


            await this.sleep(300);



        } catch (error) {


        }
    }

    async processProductPrices(priceOption) {
        try {


            this.currentOperation = 'processProductPrices';

            const isEditorOpen = document.querySelector('.product-editor') !== null;


            if (!isEditorOpen) {


                const productTypes = [
                    'STANDARD_TSHIRT',
                    'PREMIUM_TSHIRT',
                    'VNECK',
                    'TANK_TOP',
                    'STANDARD_LONG_SLEEVE',
                    'RAGLAN',
                    'STANDARD_SWEATSHIRT',
                    'STANDARD_PULLOVER_HOODIE',
                    'ZIP_HOODIE'
                ];

                let editButton = null;
                let productToEdit = null;

                for (const productType of productTypes) {
                    const button = document.querySelector(`.${productType}-edit-btn`);
                    if (button && !button.disabled && !button.classList.contains('disabled')) {

                        const productCard = button.closest('.product-card');
                        const isCardDisabled = productCard && productCard.classList.contains('disabled');

                        if (!isCardDisabled) {
                            editButton = button;
                            productToEdit = productType;
                            break;
                        }
                    }
                }

                if (editButton) {


                    if (productToEdit === 'STANDARD_TSHIRT') {
                        this.lastClickedProduct = 'Standard T-shirt';
                    } else if (productToEdit === 'PREMIUM_TSHIRT') {
                        this.lastClickedProduct = 'Premium T-shirt';
                    } else if (productToEdit === 'OVERSIZED_TSHIRT') {
                        this.lastClickedProduct = 'Comfort Colors Heavyweight T-shirt';
                    } else if (productToEdit === 'VNECK') {
                        this.lastClickedProduct = 'V-neck';
                    } else if (productToEdit === 'TANK_TOP') {
                        this.lastClickedProduct = 'Tank Top';
                    } else if (productToEdit === 'STANDARD_LONG_SLEEVE') {
                        this.lastClickedProduct = 'Long Sleeve';
                    } else if (productToEdit === 'RAGLAN') {
                        this.lastClickedProduct = 'Raglan';
                    } else if (productToEdit === 'STANDARD_SWEATSHIRT') {
                        this.lastClickedProduct = 'Sweatshirt';
                    } else if (productToEdit === 'STANDARD_PULLOVER_HOODIE') {
                        this.lastClickedProduct = 'Pullover Hoodie';
                    } else if (productToEdit === 'ZIP_HOODIE') {
                        this.lastClickedProduct = 'Zip Hoodie';
                    }

                    this.updateProgressUI(`Processing ${productToEdit}`, false);


                    editButton.click();


                    await this.sleep(750);
                } else {

                    return;
                }
            } else {

            }


            const priceContainer = await this.waitForElement('.product-price-actions');

            if (!priceContainer) {

                return;
            }



            let priceButton;

            if (priceOption === 'Default Prices') {
                priceButton = document.querySelector('#product-default-prices-btn');

            } else if (priceOption === 'Suggested Prices') {
                priceButton = document.querySelector('#product-suggested-prices-btn');

            } else if (priceOption === 'Market Average') {
                priceButton = document.querySelector('#product-market-average-btn');

            } else if (priceOption === 'Max Prices') {
                priceButton = document.querySelector('#product-max-prices-btn');

            }

            if (!priceButton) {


                if (priceOption === 'Default Prices') {
                    priceButton = document.querySelector('button[id="product-default-prices-btn"]');

                } else if (priceOption === 'Suggested Prices') {
                    priceButton = document.querySelector('button[id="product-suggested-prices-btn"]');

                } else if (priceOption === 'Market Average') {
                    priceButton = document.querySelector('button[id="product-market-average-btn"]');

                } else if (priceOption === 'Max Prices') {
                    priceButton = document.querySelector('button[id="product-max-prices-btn"]');

                }

                if (!priceButton) {

                    return;
                }
            }

            const priceButtonId = priceButton.id;



            priceButton.click();


            let buttonDisabled = false;
            let checkCount = 0;
            const maxChecks = 10;

            while (!buttonDisabled && checkCount < maxChecks) {
                await this.sleep(200);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonDisabled = currentButton.disabled ||
                                    currentButton.classList.contains('disabled') ||
                                    currentButton.getAttribute('disabled') !== null;

                    if (buttonDisabled) {

                    }
                }
                checkCount++;
            }


            let buttonEnabled = false;
            checkCount = 0;
            const maxWaitChecks = 30;

            while (!buttonEnabled && checkCount < maxWaitChecks) {
                await this.sleep(300);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonEnabled = !currentButton.disabled &&
                                   !currentButton.classList.contains('disabled') &&
                                   currentButton.getAttribute('disabled') === null;

                    if (buttonEnabled) {

                    }
                }
                checkCount++;
            }

            if (!buttonEnabled) {

            }


            await this.sleep(250);



        } catch (error) {


        }
    }

    async processScalableProducts() {
        try {


            const settings = this.settings.scalableProducts;

            if (!settings || settings.isActive !== true) {

                return;
            }

            const scalableProductTypes = [
                'POP_SOCKET',
                'PHONE_CASE_APPLE_IPHONE',
                'TOTE_BAG',
                'THROW_PILLOW'
            ];

            for (const productType of scalableProductTypes) {


                const editButton = document.querySelector(`.${productType}-edit-btn`);
                if (editButton) {


                    const hasNonDefaultSettings =
                        settings.scale !== 'Skip' ||
                        settings.color !== 'Default (White)' ||
                        settings.prices !== 'Skip';

                    if (hasNonDefaultSettings) {


                        const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                        const productCard = editButton.closest('.product-card');
                        const isCardDisabled = productCard && productCard.classList.contains('disabled');

                        if (isButtonDisabled || isCardDisabled) {

                            continue;
                        }

                        if (productType === 'POP_SOCKET') {
                            this.lastClickedProduct = 'POP_SOCKET';
                        } else if (productType === 'PHONE_CASE_APPLE_IPHONE') {
                            this.lastClickedProduct = 'PHONE_CASE';
                        } else if (productType === 'TOTE_BAG') {
                            this.lastClickedProduct = 'TOTE_BAG';
                        } else if (productType === 'THROW_PILLOW') {
                            this.lastClickedProduct = 'THROW_PILLOW';
                        } else {
                            this.lastClickedProduct = productType;
                        }

                        this.updateProgressUI(`Processing ${this.lastClickedProduct}`, false);


                        editButton.click();


                        await this.sleep(750);

                        if (settings.scale !== 'Skip') {

                            await this.processScalableProductScaleWithoutEdit(productType, settings.scale, settings.customScale);


                            await this.sleep(250);
                        }

                        if (settings.color !== 'Default (White)') {

                            await this.processScalableProductColorWithoutEdit(productType, settings.color, settings.customColor);


                            await this.sleep(250);
                        }

                        if (settings.prices !== 'Skip') {

                            await this.processScalableProductPricesWithoutEdit(productType, settings.prices);


                            await this.sleep(250);
                        }
                    } else {

                    }
                } else {

                }
            }


        } catch (error) {


        }
    }

    async processScalableProductScaleWithoutEdit(productType, scaleOption, customScale) {
        try {


            this.currentOperation = 'processScalableProductScale';


            let scaleInput;

            if (scaleOption === 'Keep current') {


            } else if (scaleOption === 'Custom scale') {
    

                const customRadio = document.querySelector('input#custom[type="radio"]');
                if (!customRadio) {

                    scaleInput = document.querySelector('input[type="radio"][id*="custom"], input[type="radio"][value*="custom"]');
                } else {
                    scaleInput = customRadio;
                }

                if (scaleInput) {

                    scaleInput.click();


                    await this.sleep(1000);

                    if (customScale) {


                        let customScaleInputField = document.querySelector('input[type="number"]:not([disabled])');

                        if (!customScaleInputField) {

                            customScaleInputField = document.querySelector('input[type="number"]');

                            if (customScaleInputField && customScaleInputField.disabled) {


                                customScaleInputField.removeAttribute('disabled');

                                await this.sleep(500);
                            }
                        }

                        if (!customScaleInputField) {


                            const alternativeInputs = [
                                document.querySelector('input.custom-scale-input'),
                                document.querySelector('input.scale-input'),
                                document.querySelector('input.scale-value'),
                                document.querySelector('input[placeholder*="scale"], input[placeholder*="Scale"]'),
                                document.querySelector('input:not([type="radio"])[id*="scale"], input:not([type="radio"])[id*="Scale"]'),
                                document.querySelector('input:not([type="radio"]):not([type="checkbox"]).ng-untouched'),
                                document.querySelector('input[min="50"][max="100"]'),
                                document.querySelector('input[style*="border-bottom: 2px solid"]')
                            ];

                            customScaleInputField = alternativeInputs.find(input => input !== null);
                        }

                        if (customScaleInputField) {


                            if (customScaleInputField.disabled) {
        
                                customScaleInputField.disabled = false;
                                customScaleInputField.removeAttribute('disabled');
                            }


                            customScaleInputField.focus();
                            await this.sleep(300);


                            customScaleInputField.value = '';
                            customScaleInputField.dispatchEvent(new Event('input', { bubbles: true }));
                            await this.sleep(300);


                            customScaleInputField.value = customScale;
                            customScaleInputField.dispatchEvent(new Event('input', { bubbles: true }));
                            customScaleInputField.dispatchEvent(new Event('change', { bubbles: true }));


                            customScaleInputField.blur();
                            await this.sleep(300);

                
                        } else {
                
                        }
                    }
                }
            } else if (scaleOption === '100%') {

                scaleInput = document.querySelector('input#percent100');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === '85%') {

                scaleInput = document.querySelector('input#percent85');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === '75%') {

                scaleInput = document.querySelector('input#percent75');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === 'Pattern') {

                scaleInput = document.querySelector('input#pattern');
                if (scaleInput) scaleInput.click();
            }

            if (!scaleInput && scaleOption !== 'Keep current') {

                return;
            }

            if (scaleOption !== 'Keep current') {
    
                await this.sleep(1000);
            }


            const resizeBtn = await this.waitForElement('.resize-btn:not([disabled])');

            if (!resizeBtn) {

                const alternativeResizeButtons = [
                    document.querySelector('button.resize-btn'),
                    document.querySelector('button[class*="resize"]'),
                    document.querySelector('button:contains("Resize")'),
                    document.querySelector('button:contains("Apply")')
                ];

                const resizeButton = alternativeResizeButtons.find(btn => btn !== null && !btn.disabled);

                if (resizeButton) {

                    resizeButton.click();
                } else {

                    return;
                }
            } else {


                resizeBtn.click();
            }


            await this.sleep(3000);


            await this.waitForElement('.delete-button');


            await this.sleep(150);



        } catch (error) {


        }
    }

    async processScalableProductColorWithoutEdit(productType, colorOption, customColor) {
        try {


            this.currentOperation = 'processScalableProductColor';

            if (colorOption === 'Default (White)') {

                return;
            }


            await this.waitForElement('.delete-button');




            if (colorOption === 'Custom Color' && customColor) {


                await this.applyCustomColorToProduct(customColor);
            } else {


                const colorSwatches = document.querySelectorAll('.color-swatch');
                let targetSwatch;

                const colorMap = {
                    'Black': '#000000',
                    'Dark Red': '#840A08',
                    'Crimson': '#C70010',
                    'Vivid Orange': '#F36900',
                    'Bright Yellow': '#FEC600',
                    'Kelly Green': '#01B62F',
                    'Forest Green': '#1C8C46',
                    'Dark Olive': '#37602B',
                    'Sky Blue': '#1AB7EA',
                    'Royal Blue': '#002BB6',
                    'Purple': '#5C2D91',
                    'Hot Pink': '#E0218A',
                    'Pale Pink': '#E9CDDB',
                    'Brown': '#7B4A1B',
                    'Gray': '#979797'
                };

                const targetColor = colorMap[colorOption];

                if (targetColor) {


                    for (const swatch of colorSwatches) {
                        const style = window.getComputedStyle(swatch);
                        const backgroundColor = style.backgroundColor;
                        const hexColor = this.rgbToHex(backgroundColor);



                        if (hexColor.toLowerCase() === targetColor.toLowerCase()) {
                            targetSwatch = swatch;

                            break;
                        }
                    }

                    if (!targetSwatch) {

                        let closestSwatch = null;
                        let closestDistance = Number.MAX_VALUE;

                        for (const swatch of colorSwatches) {
                            const style = window.getComputedStyle(swatch);
                            const backgroundColor = style.backgroundColor;
                            const hexColor = this.rgbToHex(backgroundColor);

                            const distance = this.calculateColorDistance(hexColor, targetColor);


                            if (distance < closestDistance) {
                                closestDistance = distance;
                                closestSwatch = swatch;
                            }
                        }

                        if (closestSwatch) {
                            targetSwatch = closestSwatch;
                            const style = window.getComputedStyle(closestSwatch);
                            const closestColor = this.rgbToHex(style.backgroundColor);

                        }
                    }

                    if (targetSwatch) {

                        targetSwatch.click();
                    } else {

                    }
                }
            }


            await this.sleep(1500);



        } catch (error) {


        }
    }

    async processScalableProductPricesWithoutEdit(productType, priceOption) {
        try {


            this.currentOperation = 'processScalableProductPrices';


            const priceContainer = await this.waitForElement('.product-price-actions');

            if (!priceContainer) {

                return;
            }



            let priceButton;

            if (priceOption === 'Default Prices') {
                priceButton = document.querySelector('#product-default-prices-btn');

            } else if (priceOption === 'Suggested Prices') {
                priceButton = document.querySelector('#product-suggested-prices-btn');

            } else if (priceOption === 'Market Average') {
                priceButton = document.querySelector('#product-market-average-btn');

            } else if (priceOption === 'Max Prices') {
                priceButton = document.querySelector('#product-max-prices-btn');

            }

            if (!priceButton) {


                if (priceOption === 'Default Prices') {
                    priceButton = document.querySelector('button[id="product-default-prices-btn"]');

                } else if (priceOption === 'Suggested Prices') {
                    priceButton = document.querySelector('button[id="product-suggested-prices-btn"]');

                } else if (priceOption === 'Market Average') {
                    priceButton = document.querySelector('button[id="product-market-average-btn"]');

                } else if (priceOption === 'Max Prices') {
                    priceButton = document.querySelector('button[id="product-max-prices-btn"]');

                }

                if (!priceButton) {

                    return;
                }
            }

            const priceButtonId = priceButton.id;



            priceButton.click();


            let buttonDisabled = false;
            let checkCount = 0;
            const maxChecks = 10;

            while (!buttonDisabled && checkCount < maxChecks) {
                await this.sleep(200);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonDisabled = currentButton.disabled ||
                                    currentButton.classList.contains('disabled') ||
                                    currentButton.getAttribute('disabled') !== null;

                    if (buttonDisabled) {

                    }
                }
                checkCount++;
            }


            let buttonEnabled = false;
            checkCount = 0;
            const maxWaitChecks = 30;

            while (!buttonEnabled && checkCount < maxWaitChecks) {
                await this.sleep(300);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonEnabled = !currentButton.disabled &&
                                   !currentButton.classList.contains('disabled') &&
                                   currentButton.getAttribute('disabled') === null;

                    if (buttonEnabled) {

                    }
                }
                checkCount++;
            }

            if (!buttonEnabled) {

            }


            await this.sleep(250);



        } catch (error) {


        }
    }

    async applyCustomColorToProduct(hexColor) {


        const cleanHex = hexColor.replace('#', '').toUpperCase();


        try {


            const colorButton = document.querySelector([
                'button#color-btn.btn.btn-secondary.icon',
                'button.btn.btn-secondary.icon[id*="color"]',
                'button.btn-secondary.icon[id*="color"]'
            ].join(','));

            if (!colorButton) {
                throw new Error();
            }

            this.addColorPickerHidingCSS();



            colorButton.click();


            await this.sleep(1000);


            let sketchPicker = document.querySelector('.sketch-picker, div[class*="sketch-picker"]');

            if (!sketchPicker) {


                await this.sleep(1000);

                sketchPicker = document.querySelector('.sketch-picker, div[class*="sketch-picker"]');

                if (!sketchPicker) {
                    throw new Error();
                }


            }


            const hexInput = sketchPicker.querySelector('.sketch-fields input');

            if (!hexInput) {
                throw new Error();
            }




            hexInput.value = '';
            hexInput.dispatchEvent(new Event('input', { bubbles: true }));
            await this.sleep(100);


            hexInput.value = cleanHex;
            hexInput.dispatchEvent(new Event('input', { bubbles: true }));
            hexInput.dispatchEvent(new Event('change', { bubbles: true }));

            await this.sleep(200);


            hexInput.dispatchEvent(new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                bubbles: true,
                cancelable: true
            }));

            await this.sleep(100);

            hexInput.dispatchEvent(new KeyboardEvent('keyup', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                bubbles: true,
                cancelable: true
            }));

            await this.sleep(500);

            await this.cleanupAfterColorApplication();


            return true;

        } catch (error) {

            this.removeColorPickerHidingCSS();

            throw error;
        }
    }

    addColorPickerHidingCSS() {

        if (!document.getElementById('temp-hide-color-picker-style')) {
            const styleEl = document.createElement('style');
            styleEl.id = 'temp-hide-color-picker-style';
            styleEl.textContent = `

                body.temp-hide-color-picker .sketch-picker,
                body.temp-hide-color-picker .popover.color-picker-popover,
                body.temp-hide-color-picker ngb-popover-window[class*="color-picker-popover"] {
                    position: absolute !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                    pointer-events: none !important;
                    height: 0 !important;
                    overflow: hidden !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    width: 0 !important;
                }

                body.temp-hide-color-picker .popover.color-picker-popover .arrow,
                body.temp-hide-color-picker ngb-popover-window[class*="color-picker-popover"] .arrow {
                    display: none !important;
                }

                .sketch-picker input {
                    pointer-events: auto !important;
                }
            `;
            document.head.appendChild(styleEl);
        }

        document.body.classList.add('temp-hide-color-picker');
    }

    removeColorPickerHidingCSS() {

        document.body.classList.remove('temp-hide-color-picker');

    }

    async cleanupAfterColorApplication() {


        document.body.dispatchEvent(new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
        }));

        await this.sleep(100);

        this.removeColorPickerHidingCSS();


    }

    async processTumblerProduct() {
        try {


            const settings = this.settings.tumblerProduct;

            if (!settings || settings.isActive !== true) {

                return;
            }

            const hasNonDefaultSettings =
                settings.sides !== 'Default (One Side)' ||
                settings.scale !== '100%' ||
                settings.colors !== 'Skip' ||
                settings.prices !== 'Skip';

            if (hasNonDefaultSettings) {


                const editButton = document.querySelector('.TUMBLER-edit-btn');
                if (!editButton) {

                    return;
                }

                const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                const productCard = editButton.closest('.product-card');
                const isCardDisabled = productCard && productCard.classList.contains('disabled');

                if (isButtonDisabled || isCardDisabled) {

                    return;
                }



                this.lastClickedProduct = 'TUMBLER';

                this.updateProgressUI(`Processing Tumbler`, false);

                editButton.click();


                await this.sleep(750);

                if (settings.colors !== 'Skip') {

                    await this.processTumblerColors(settings.colors);


                    await this.sleep(250);
                } else {

                }

                if (settings.prices !== 'Skip') {

                    await this.processTumblerPrices(settings.prices);


                    await this.sleep(250);
                } else {

                }


                if (settings.sides !== 'Default (One Side)') {

                    await this.processTumblerSides(settings.sides);

                    await this.sleep(500);
                } else {

                }

                await this.processTumblerScale(settings.scale, settings.customScale);

                await this.sleep(250);


            } else {

            }
        } catch (error) {


        }
    }

    async processMugProduct() {
        try {


            const settings = this.settings.mugProducts;

            if (!settings || settings.isActive !== true) {

                return;
            }


            const hasNonDefaultSettings =
                settings.sides !== 'Default (One Side)' ||
                settings.scale !== '100%' ||
                settings.colors !== 'Skip' ||
                settings.prices !== 'Skip';



            if (hasNonDefaultSettings) {


                const editButton = document.querySelector('.MUG-edit-btn');
                if (!editButton) {

                    return;
                }

                const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                const productCard = editButton.closest('.product-card');
                const isCardDisabled = productCard && productCard.classList.contains('disabled');

                if (isButtonDisabled || isCardDisabled) {

                    return;
                }




                this.lastClickedProduct = 'MUG';

                this.updateProgressUI('Processing Ceramic Mug', false);

                editButton.click();



                await this.sleep(750);

                if (settings.sides !== 'Default (One Side)') {

                    await this.processMugSides(settings.sides);
                    await this.sleep(500);
                } else {

                }

                if (settings.prices !== 'Skip') {

                    await this.processMugPrices(settings.prices);
                    await this.sleep(250);
                } else {

                }

                if (settings.colors && settings.colors !== 'Skip') {

                    await this.processMugColors(settings.colors);
                    await this.sleep(250);
                } else {

                }

                await this.processMugScale(settings.scale, settings.customScale);
                await this.sleep(250);

                // Final resize-btn click to apply all design changes together
                const resizeBtn = await this.waitForElement('.resize-btn:not([disabled])');

                if (!resizeBtn) {

                    const alternativeResizeButtons = [
                        document.querySelector('button.resize-btn'),
                        document.querySelector('button[class*="resize"]'),
                        document.querySelector('button:contains("Resize")'),
                        document.querySelector('button:contains("Apply")')
                    ];

                    const resizeButton = alternativeResizeButtons.find(btn => btn !== null && !btn.disabled);

                    if (resizeButton) {

                        await retryWithBackoff(() => { resizeButton.click(); return Promise.resolve(); });
                    } else {

                    }
                } else {

                    await retryWithBackoff(() => { resizeBtn.click(); return Promise.resolve(); });
                }


                try {

                    await this.waitForAnyGeneratingMessage(60000);


                    await this.waitForGeneratingImageMessages();

                } catch (waitError) {


                }

            } else {


            }
        } catch (error) {


        }
    }


    async getAutomationState() {
        return new Promise((resolve) => {
            chrome.runtime.sendMessage({ action: 'getAutomationState' }, (response) => {
                if (response && response.success && response.state) {
                    resolve(response.state);
                } else {
                    resolve(null);
                }
            });
        });
    }

    async processNextFile() {
        try {



            let isCompleted = false;

            await new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    action: 'getAutomationState'
                }, (response) => {
                    if (response && response.success && response.state) {
                        const state = response.state;
                        if (state.currentFile &&
                            state.currentFile.filename === this.currentFile?.filename &&
                            state.currentFile.status === 'completed') {

                            isCompleted = true;
                        } else {

                        }
                    } else {

                    }
                    resolve();
                });
            });

            if (!isCompleted) {

                await new Promise((resolve) => {
                    chrome.runtime.sendMessage({
                        action: 'markTabCompleted',
                        filename: this.currentFile?.filename
                    }, (response) => {
                        if (response && response.success) {

                            isCompleted = true;
                        } else {

                        }
                        resolve();
                    });
                });
            }

            const state = await this.getAutomationState();
            const isFinalFile = state &&
                               state.totalFiles &&
                               state.processedFiles &&
                               (state.processedFiles.length + 1 >= state.totalFiles);



            const currentFileIndex = this.currentFile?.index + 1 || 1;
            const totalFiles = state?.totalFiles || this.settings?.totalFiles || 0;

            const percentPerFile = 100 / totalFiles;
            const exactFilePercentage = currentFileIndex * percentPerFile;



            const statusInfo = {
                status: 'Completed',
                statusIndex: 21,
                totalStatuses: 22,
                isCompleted: true
            };


            await new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    action: 'updateAutomationProgress',
                    status: 'Completed',
                    filename: this.currentFile?.filename,
                    currentFile: currentFileIndex,
                    totalFiles: totalFiles,
                    isTabCompleted: false,
                    updateProgressOnly: true,
                    updateProgressBar: true,
                    forceProgressUpdate: true,
                    completed: isFinalFile,
                    statusInfo: statusInfo
                }, (response) => {

                    resolve();
                });
            });


            await this.sleep(250);

            if (isFinalFile) {


                await this.sleep(1000);


                await new Promise((resolve) => {
                    chrome.runtime.sendMessage({
                        action: 'hideAllProgressUI'
                    }, (response) => {

                        resolve();
                    });
                });

                return true;
            }


            const result = await new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    action: 'processNextFile',
                    waitForCompletion: true
                }, (response) => {
                    if (response && response.success) {

                    } else {

                    }
                    resolve(response && response.success);
                });
            });

            return result;
        } catch (error) {

            throw error;
        }
    }

    updateProgressUI(status, isTabCompleted = false) {


        const orderedStatuses = [
            'Uploading...',
            'Processing Products',
            'Processing Design',
            'Processing Standard T-shirt',
            'Processing Premium T-shirt',
            'Processing CC Heavyweight T-shirt',
            'Processing V-neck T-shirt',
            'Processing Tank Top',
            'Processing Long Sleeve Shirt',
            'Processing Raglan',
            'Processing Pullover Hoodie',
            'Processing Zip Hoodie',
            'Processing Standard Sweatshirt',
            'Processing Scalable Products',
            'Processing PopSockets Grip',
            'Processing iPhone Case',
            'Processing Tote Bag',
            'Processing Throw Pillow',
            'Processing Tumbler',
            'Processing Ceramic Mug',
            'Actions: Text Swap',
            'Actions: Copy EN to All',
            'Actions: Save to Drafts',
            'Completed'
        ];

        let formattedStatus = status;

        if (status === 'Uploading') {
            formattedStatus = 'Uploading...';
        } else if (status === 'Completed') {
            formattedStatus = 'Completed';
        } else if (status === 'Text Swap') {
            formattedStatus = 'Actions: Text Swap';
        } else if (status === 'Copy EN to All') {
            formattedStatus = 'Actions: Copy EN to All';
        } else if (status === 'Save to Drafts') {
            formattedStatus = 'Actions: Save to Drafts';
        }

        else if (status.startsWith('Processing ')) {

            if (status.includes('STANDARD_TSHIRT')) {
                formattedStatus = 'Processing Standard T-shirt';
            } else if (status.includes('PREMIUM_TSHIRT')) {
                formattedStatus = 'Processing Premium T-shirt';
            } else if (status.includes('OVERSIZED_TSHIRT')) {
                formattedStatus = 'Processing CC Heavyweight T-shirt';
            } else if (status.includes('VNECK')) {
                formattedStatus = 'Processing V-neck T-shirt';
            } else if (status.includes('TANK_TOP')) {
                formattedStatus = 'Processing Tank Top';
            } else if (status.includes('STANDARD_LONG_SLEEVE')) {
                formattedStatus = 'Processing Long Sleeve Shirt';
            } else if (status.includes('RAGLAN')) {
                formattedStatus = 'Processing Raglan';
            } else if (status.includes('STANDARD_PULLOVER_HOODIE')) {
                formattedStatus = 'Processing Pullover Hoodie';
            } else if (status.includes('ZIP_HOODIE')) {
                formattedStatus = 'Processing Zip Hoodie';
            } else if (status.includes('STANDARD_SWEATSHIRT')) {
                formattedStatus = 'Processing Standard Sweatshirt';
            } else if (status.includes('POP_SOCKET')) {
                formattedStatus = 'Processing PopSockets Grip';
            } else if (status.includes('PHONE_CASE')) {
                formattedStatus = 'Processing iPhone Case';
            } else if (status.includes('TOTE_BAG')) {
                formattedStatus = 'Processing Tote Bag';
            } else if (status.includes('THROW_PILLOW')) {
                formattedStatus = 'Processing Throw Pillow';
            } else if (status.includes('TUMBLER')) {
                formattedStatus = 'Processing Tumbler';
            } else if (status.includes('MUG')) {
                formattedStatus = 'Processing Ceramic Mug';
            }

            else if (this.currentOperation) {
                if (this.lastClickedProduct) {
                    formattedStatus = `Processing ${this.lastClickedProduct}`;
                } else {

                    if (this.currentOperation.includes('Tumbler')) {
                        formattedStatus = 'Processing Tumbler';
                    } else if (this.currentOperation.includes('Scalable')) {
                        formattedStatus = 'Processing PopSockets Grip';
                    } else {

                        formattedStatus = 'Processing Design';
                    }
                }
            } else {

                formattedStatus = 'Processing Design';
            }
        } else if (status === 'Processing Products') {

            formattedStatus = 'Processing Products';
        }



        const statusIndex = orderedStatuses.indexOf(formattedStatus);
        const totalStatuses = orderedStatuses.length;

        const statusInfo = {
            status: formattedStatus,
            statusIndex: statusIndex !== -1 ? statusIndex : 0,
            totalStatuses: totalStatuses,
            isCompleted: formattedStatus === 'Completed'
        };



        const updateProgressBar = formattedStatus === 'Completed' ? true : false;

        chrome.runtime.sendMessage({ action: 'getAutomationState' }, (response) => {
            if (response && response.success && response.state) {
                const state = response.state;
                const totalFiles = state.totalFiles || this.settings?.totalFiles || 0;
                const processedFiles = state.processedFiles?.length || 0;
                const currentFileIndex = this.currentFile?.index !== undefined ? this.currentFile.index + 1 : 1;

                const queueProgress = {
                    current: currentFileIndex,
                    total: totalFiles,
                    processed: processedFiles,
                    remaining: totalFiles - processedFiles - 1
                };



                chrome.runtime.sendMessage({
                    action: 'updateAutomationProgress',
                    status: formattedStatus,
                    filename: this.currentFile?.filename,
                    currentFile: currentFileIndex,
                    totalFiles: totalFiles,
                    isTabCompleted: isTabCompleted,
                    updateProgressOnly: true,
                    updateProgressBar,
                    forceProgressUpdate: formattedStatus === 'Completed',
                    queueProgress: queueProgress,
                    statusInfo: statusInfo
                }, (response) => {


                    if (formattedStatus === 'Completed' && currentFileIndex >= totalFiles) {
                        this.playCompletionSound();
                    }
                });
            } else {

                chrome.runtime.sendMessage({
                    action: 'updateAutomationProgress',
                    status: formattedStatus,
                    filename: this.currentFile?.filename,
                    currentFile: this.currentFile?.index + 1,
                    totalFiles: this.settings?.totalFiles || 0,
                    isTabCompleted: isTabCompleted,
                    updateProgressOnly: true,
                    updateProgressBar,
                    forceProgressUpdate: formattedStatus === 'Completed',
                    statusInfo: statusInfo
                }, (response) => {


                    if (formattedStatus === 'Completed' && (this.currentFile?.index + 1) >= (this.settings?.totalFiles || 0)) {
                        this.playCompletionSound();
                    }
                });
            }
        });
    }

    updateStatusOnly(status) {
        try {
            chrome.runtime.sendMessage({
                action: 'updateAutomationProgress',
                status: status
            }, () => {});
        } catch (e) {}
    }

    getCurrentProductName() {
        try {

            const productEditor = document.querySelector('.product-editor');
            if (productEditor) {

                const productTitle = productEditor.querySelector('.product-title');
                if (productTitle && productTitle.textContent) {

                    const productName = productTitle.textContent.trim().replace(/\s+by\s+Amazon$/i, '');

                    if (productName.includes('Comfort Colors') || productName.includes('Heavyweight')) return 'OVERSIZED_TSHIRT';
                    if (productName.includes('Premium') && productName.includes('T-shirt')) return 'PREMIUM_TSHIRT';
                    if (productName.includes('T-shirt')) return 'STANDARD_TSHIRT';
                    if (productName.includes('V-neck')) return 'VNECK';
                    if (productName.includes('Tank Top')) return 'TANK_TOP';
                    if (productName.includes('Long Sleeve')) return 'STANDARD_LONG_SLEEVE';
                    if (productName.includes('Raglan')) return 'RAGLAN';
                    if (productName.includes('Pullover Hoodie')) return 'STANDARD_PULLOVER_HOODIE';
                    if (productName.includes('Zip Hoodie')) return 'ZIP_HOODIE';
                    if (productName.includes('Sweatshirt')) return 'STANDARD_SWEATSHIRT';

                    return productName.toUpperCase().replace(/\s+/g, '_');
                }

                const editorClasses = productEditor.className.split(' ');
                for (const cls of editorClasses) {
                    if (cls.includes('OVERSIZED_TSHIRT')) return 'OVERSIZED_TSHIRT';
                    if (cls.includes('STANDARD_TSHIRT')) return 'STANDARD_TSHIRT';
                    if (cls.includes('PREMIUM_TSHIRT')) return 'PREMIUM_TSHIRT';
                    if (cls.includes('VNECK')) return 'VNECK';
                    if (cls.includes('TANK_TOP')) return 'TANK_TOP';
                    if (cls.includes('LONG_SLEEVE')) return 'STANDARD_LONG_SLEEVE';
                    if (cls.includes('RAGLAN')) return 'RAGLAN';
                    if (cls.includes('PULLOVER_HOODIE')) return 'STANDARD_PULLOVER_HOODIE';
                    if (cls.includes('ZIP_HOODIE')) return 'ZIP_HOODIE';
                    if (cls.includes('SWEATSHIRT')) return 'STANDARD_SWEATSHIRT';
                }
            }

            if (this.lastClickedProduct) {
                return this.lastClickedProduct;
            }

            return null;
        } catch (error) {

            return null;
        }
    }

    getScalableProductName() {
        try {

            const productEditor = document.querySelector('.product-editor');
            if (productEditor) {

                const productTitle = productEditor.querySelector('.product-title');
                if (productTitle && productTitle.textContent) {

                    const productName = productTitle.textContent.trim().replace(/\s+by\s+Amazon$/i, '');

                    if (productName.includes('PopSockets')) return 'POP_SOCKET';
                    if (productName.includes('Phone Case')) return 'PHONE_CASE';
                    if (productName.includes('Mouse Pad')) return 'MOUSEPAD';
                    if (productName.includes('Tote Bag')) return 'TOTE_BAG';
                    if (productName.includes('Throw Pillow')) return 'THROW_PILLOW';

                    return productName.toUpperCase().replace(/\s+/g, '_');
                }

                const editorClasses = productEditor.className.split(' ');
                for (const cls of editorClasses) {
                    if (cls.includes('POPSOCKET')) return 'POP_SOCKET';
                    if (cls.includes('PHONE_CASE')) return 'PHONE_CASE';
                    if (cls.includes('MOUSEPAD')) return 'MOUSEPAD';
                    if (cls.includes('TOTE_BAG')) return 'TOTE_BAG';
                    if (cls.includes('THROW_PILLOW')) return 'THROW_PILLOW';
                }
            }

            return null;
        } catch (error) {

            return null;
        }
    }

    playCompletionSound() {


        chrome.runtime.sendMessage({ action: 'getAutomationState' }, (response) => {
            if (response && response.success && response.state) {
                const state = response.state;
                const currentFileIndex = this.currentFile?.index + 1 || 1;
                const totalFiles = state.totalFiles || this.settings?.totalFiles || 0;

                const isLastFile = currentFileIndex >= totalFiles;


                chrome.runtime.sendMessage({
                    action: 'playCompletionSound',
                    isLastFile: isLastFile
                });
            } else {

                chrome.runtime.sendMessage({
                    action: 'playCompletionSound'
                });
            }
        });
    }

    handleError(error) {


        this.retryCount++;

        if (this.retryCount <= this.maxRetries) {


            setTimeout(() => this.startAutomation(), 5000);
        } else {


            this.updateProgressUI('Error: ' + error.message, true);

            chrome.runtime.sendMessage({
                action: 'showErrorNotification',
                filename: this.currentFile?.filename,
                error: error.message
            });


            chrome.runtime.sendMessage({
                action: 'markTabCompleted',
                filename: this.currentFile?.filename,
                status: 'error',
                error: error.message
            }, (response) => {



                setTimeout(() => {

                    this.processNextFile();
                }, 1000);
            });
        }
    }

    async waitForElement(selector, timeout = 300000) {

        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            let lastStatusUpdate = 0;

            const existingElement = document.querySelector(selector);
            if (existingElement) {
                resolve(existingElement);
                return;
            }

            const checkElement = () => {
                const element = document.querySelector(selector);

                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`Timeout waiting for ${selector} after ${Math.floor(timeout/1000)}s`));
                } else {
                    if (Date.now() - lastStatusUpdate > 15000) {
                        lastStatusUpdate = Date.now();
                        try { this.updateStatusOnly('Waiting..'); } catch (e) {}
                    }
                    setTimeout(checkElement, 500);
                }
            };

            checkElement();
        });
    }

    async blobUrlToFile(blobUrl, filename) {
        try {
            const response = await fetch(blobUrl);
            const blob = await response.blob();
            return new File([blob], filename, { type: blob.type });
        } catch (error) {

            throw error;
        }
    }

    base64ToFile(base64, filename) {
        const arr = base64.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);

        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }

        return new File([u8arr], filename, { type: mime });
    }

    cleanFilenameForTextSwap(filename) {

        let cleanName = filename.replace(/\.[^/.]+$/, '');

        cleanName = cleanName.replace(/[-_]+$/, '');

        if (cleanName.length > 1 || /[-_\s(]/.test(cleanName)) {

            cleanName = cleanName.replace(/(?:[-_\s]*\d+|[-_\s]*\(\d+\))$/, '');
        }

        return cleanName.trim();
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async waitForGeneratingImageMessages() {
        try {


            this.generatingImageCount = 0;

            return new Promise((resolve, reject) => {
                const startTime = Date.now();
                const timeout = 300000;

                const checkForMessages = () => {

                    let messageVisible = false;
                    let detectedMessage = '';

                    for (const message of this.productSelectors.generatingMessages) {
                        if (document.body.textContent.includes(message)) {
                            messageVisible = true;
                            detectedMessage = message;
                            break;
                        }
                    }

                    if (messageVisible) {


                        this.generatingImageCount++;


                        const waitForDisappear = () => {

                            let stillVisible = false;

                            for (const message of this.productSelectors.generatingMessages) {
                                if (document.body.textContent.includes(message)) {
                                    stillVisible = true;
                                    break;
                                }
                            }

                            if (!stillVisible) {


                                if (this.generatingImageCount >= this.requiredGeneratingImageCount) {


                                    setTimeout(() => {
                                        resolve();
                                    }, 300);
                                } else {

                                    setTimeout(checkForMessages, 500);
                                }
                            } else if (Date.now() - startTime > timeout) {
                                reject(new Error());
                            } else {

                                setTimeout(waitForDisappear, 500);
                            }
                        };

                        setTimeout(waitForDisappear, 500);
                    } else if (Date.now() - startTime > timeout) {
                        reject(new Error());
                    } else {

                        setTimeout(checkForMessages, 500);
                    }
                };

                checkForMessages();
            });
        } catch (error) {

            throw error;
        }
    }

    async processMugColors(colorOption) {
        try {


            this.currentOperation = 'processMugColors';

            const isEditorOpen = document.querySelector('.product-editor') !== null;


            if (!isEditorOpen) {


                const editButton = document.querySelector('.MUG-edit-btn');

                if (!editButton) {

                    return;
                }

                const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                const productCard = editButton.closest('.product-card');
                const isCardDisabled = productCard && productCard.classList.contains('disabled');

                if (isButtonDisabled || isCardDisabled) {

                    return;
                }


                editButton.click();


                await this.sleep(750);
            }


            const colorConversionContainer = await this.waitForElement('.color-conversion-container');

            if (!colorConversionContainer) {

                return;
            }


            const containerStyle = window.getComputedStyle(colorConversionContainer);
            if (containerStyle.display === 'none' || containerStyle.visibility === 'hidden') {

                await this.sleep(500);
                const updatedStyle = window.getComputedStyle(colorConversionContainer);
                if (updatedStyle.display === 'none' || updatedStyle.visibility === 'hidden') {
                    return;
                }
            }

            const dropdown = colorConversionContainer.querySelector('.snap-dropdown');
            if (!dropdown) {

                return;
            }

            const dropdownHeader = dropdown.querySelector('.dropdown-header');
            if (!dropdownHeader) {

                return;
            }


            dropdownHeader.click();


            await this.sleep(300);


            const dropdownMenu = dropdown.querySelector('.dropdown-menu');
            if (!dropdownMenu) {

                return;
            }


            const isMenuOpen = dropdownMenu.classList.contains('show');
            if (!isMenuOpen) {

                await this.sleep(200);
                if (!dropdownMenu.classList.contains('show')) {
                    return;
                }
            }


            const dropdownItems = dropdownMenu.querySelectorAll('.dropdown-item');
            let targetItem = null;


            const optionMapping = {
                'Original': 'original',
                'Invert to 100% Black': 'invert',
                'Smart Invert to Black': 'smart',
                'Black Outline': 'blackBackground'
            };

            const targetValue = optionMapping[colorOption];

            for (const item of dropdownItems) {
                const itemText = item.textContent.trim();
                const itemValue = item.dataset.value;

                if (itemText === colorOption || itemValue === targetValue) {
                    targetItem = item;
                    break;
                }
            }

            if (!targetItem) {

                return;
            }


            targetItem.click();


            await this.sleep(300);


            await this.sleep(200);



        } catch (error) {


        }
    }

    async processMugPrices(priceOption) {
        try {


            this.currentOperation = 'processMugPrices';

            const isEditorOpen = document.querySelector('.product-editor') !== null;


            if (!isEditorOpen) {


                const editButton = document.querySelector('.MUG-edit-btn');

                if (!editButton) {

                    return;
                }

                const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                const productCard = editButton.closest('.product-card');
                const isCardDisabled = productCard && productCard.classList.contains('disabled');

                if (isButtonDisabled || isCardDisabled) {

                    return;
                }


                editButton.click();


                await this.sleep(750);
            }


            const priceContainer = await this.waitForElement('.product-price-actions');

            if (!priceContainer) {

                return;
            }



            let priceButton;

            if (priceOption === 'Default Prices') {
                priceButton = document.querySelector('#product-default-prices-btn');

            } else if (priceOption === 'Suggested Prices') {
                priceButton = document.querySelector('#product-suggested-prices-btn');

            } else if (priceOption === 'Market Average') {
                priceButton = document.querySelector('#product-market-average-btn');

            } else if (priceOption === 'Max Prices') {
                priceButton = document.querySelector('#product-max-prices-btn');

            }

            if (!priceButton) {


                if (priceOption === 'Default Prices') {
                    priceButton = document.querySelector('button[id="product-default-prices-btn"]');

                } else if (priceOption === 'Suggested Prices') {
                    priceButton = document.querySelector('button[id="product-suggested-prices-btn"]');

                } else if (priceOption === 'Market Average') {
                    priceButton = document.querySelector('button[id="product-market-average-btn"]');

                } else if (priceOption === 'Max Prices') {
                    priceButton = document.querySelector('button[id="product-max-prices-btn"]');

                }

                if (!priceButton) {

                    return;
                }
            }

            const priceButtonId = priceButton.id;



            priceButton.click();


            let buttonDisabled = false;
            let checkCount = 0;
            const maxChecks = 10;

            while (!buttonDisabled && checkCount < maxChecks) {
                await this.sleep(200);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonDisabled = currentButton.disabled ||
                                    currentButton.classList.contains('disabled') ||
                                    currentButton.getAttribute('disabled') !== null;

                    if (buttonDisabled) {

                    }
                }
                checkCount++;
            }


            let buttonEnabled = false;
            checkCount = 0;
            const maxWaitChecks = 30;

            while (!buttonEnabled && checkCount < maxWaitChecks) {
                await this.sleep(300);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonEnabled = !currentButton.disabled &&
                                   !currentButton.classList.contains('disabled') &&
                                   currentButton.getAttribute('disabled') === null;

                    if (buttonEnabled) {

                    }
                }
                checkCount++;
            }

            if (!buttonEnabled) {

            }


            await this.sleep(250);



        } catch (error) {


        }
    }

    async processMugSides(sidesOption) {
        try {

            this.currentOperation = 'processMugSides';

            const tabs = Array.from(document.querySelectorAll('.tab-button'));
            const available = tabs.map(btn => btn.textContent?.trim()).filter(Boolean);


            let tabButton;

            if (sidesOption === 'Both Sides' || sidesOption === 'Two Sides') {
                tabButton = tabs.find(btn => {
                    const text = btn.textContent.trim();
                    return text.includes('Two Sides') || text.includes('Both Sides');
                });

            } else if (sidesOption === 'Default (One Side)') {
                tabButton = tabs.find(
                    btn => btn.textContent.trim().includes('One Side') || btn.textContent.trim() === 'Front'
                );

            }

            if (!tabButton) {


                return;
            }

            if (tabButton.classList.contains('active')) {


            } else {


                tabButton.click();


                await this.sleep(500);
            }



            await this.sleep(150);




        } catch (error) {

            throw error;
        }
    }

    async processMugScale(scaleOption, customScale) {
        try {


            this.currentOperation = 'processMugScale';

            const isEditorOpen = document.querySelector('.product-editor') !== null;


            if (!isEditorOpen) {


                const editButton = document.querySelector('.MUG-edit-btn');

                if (!editButton) {

                    return;
                }

                const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                const productCard = editButton.closest('.product-card');
                const isCardDisabled = productCard && productCard.classList.contains('disabled');

                if (isButtonDisabled || isCardDisabled) {

                    return;
                }


                editButton.click();


                await this.sleep(1200);
            }



            const mugSettings = this.settings.mugProducts || {};
            const desiredSide = mugSettings.sides || 'Default (One Side)';


            const sideTabButtons = Array.from(document.querySelectorAll('.tab-button'));


            if (sideTabButtons.length > 0) {

                sideTabButtons.forEach(btn => {

                });
            }

            const activeSideTab = sideTabButtons.find(btn => btn.classList.contains('active'));

            let exactMatchTab = null;

            if (desiredSide === 'Two Sides') {

                exactMatchTab = sideTabButtons.find(btn => btn.textContent.trim() === 'Two Sides');

            } else if (desiredSide === 'Both Sides') {

                exactMatchTab = sideTabButtons.find(btn => btn.textContent.trim() === 'Both Sides');

            } else if (desiredSide === 'Default (One Side)') {

                exactMatchTab = sideTabButtons.find(btn =>
                    btn.textContent.trim() === 'One Side' ||
                    btn.textContent.trim() === 'Default (One Side)' ||
                    btn.textContent.trim() === 'Front'
                );

            }

            if (exactMatchTab && (!activeSideTab || activeSideTab.textContent.trim() !== exactMatchTab.textContent.trim())) {




                await this.sleep(1200);
            }

            else if (!exactMatchTab && !activeSideTab && sideTabButtons.length > 0) {


                const firstTab = sideTabButtons[0];




                await this.sleep(1200);
            }

            else if (activeSideTab) {
                if (exactMatchTab && activeSideTab.textContent.trim() === exactMatchTab.textContent.trim()) {

                } else {

                }
            } else {

            }


            let scaleInput;

            if (scaleOption === 'Keep current') {


            } else if (scaleOption === 'Custom scale') {


                const customRadio = document.querySelector('input#custom[type="radio"]');
                if (!customRadio) {

                    scaleInput = document.querySelector('input[type="radio"][id*="custom"], input[type="radio"][value*="custom"]');
                } else {
                    scaleInput = customRadio;
                }

                if (scaleInput) {

                    scaleInput.click();


                    await this.sleep(500);

                    if (customScale) {


                        let customScaleInputField = document.querySelector('input[type="number"]:not([disabled])');

                        if (!customScaleInputField) {

                            customScaleInputField = document.querySelector('input[type="number"]');

                            if (customScaleInputField && customScaleInputField.disabled) {


                                customScaleInputField.removeAttribute('disabled');

                                await this.sleep(500);
                            }
                        }

                        if (!customScaleInputField) {


                            const alternativeInputs = [
                                document.querySelector('input.custom-scale-input'),
                                document.querySelector('input.scale-input'),
                                document.querySelector('input.scale-value'),
                                document.querySelector('input[placeholder*="scale"], input[placeholder*="Scale"]'),
                                document.querySelector('input:not([type="radio"])[id*="scale"], input:not([type="radio"])[id*="Scale"]'),
                                document.querySelector('input:not([type="radio"]):not([type="checkbox"]).ng-untouched'),
                                document.querySelector('input[min="50"][max="100"]'),
                                document.querySelector('input[style*="border-bottom: 2px solid"]')
                            ];

                            customScaleInputField = alternativeInputs.find(input => input !== null);
                        }

                        if (customScaleInputField) {


                            if (customScaleInputField.disabled) {

                                customScaleInputField.disabled = false;
                                customScaleInputField.removeAttribute('disabled');
                            }


                            customScaleInputField.focus();
                            await this.sleep(300);


                            customScaleInputField.value = '';
                            customScaleInputField.dispatchEvent(new Event('input', { bubbles: true }));
                            await this.sleep(300);


                            customScaleInputField.value = customScale;
                            customScaleInputField.dispatchEvent(new Event('input', { bubbles: true }));
                            customScaleInputField.dispatchEvent(new Event('change', { bubbles: true }));


                            customScaleInputField.blur();
                            await this.sleep(300);


                        } else {
    
                        }
                    }
                }
            } else if (scaleOption === '100%') {

                scaleInput = document.querySelector('input#percent100');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === '85%') {

                scaleInput = document.querySelector('input#percent85');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === '75%') {

                scaleInput = document.querySelector('input#percent75');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === 'Pattern') {

                scaleInput = document.querySelector('input#pattern');
                if (scaleInput) scaleInput.click();
            }

            if (!scaleInput && scaleOption !== 'Keep current') {

                return;
            }

            if (scaleOption !== 'Keep current') {

                await this.sleep(1000);
            }



        } catch (error) {


        }
    }

    async processTumblerColors(colorOption) {
        try {


            this.currentOperation = 'processTumblerColors';

            const isEditorOpen = document.querySelector('.product-editor') !== null;


            if (!isEditorOpen) {


                const editButton = document.querySelector('.TUMBLER-edit-btn');

                if (!editButton) {

                    return;
                }

                const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                const productCard = editButton.closest('.product-card');
                const isCardDisabled = productCard && productCard.classList.contains('disabled');

                if (isButtonDisabled || isCardDisabled) {

                    return;
                }


                editButton.click();


                await this.sleep(750);
            }


            const colorContainer = await this.waitForElement('.color-groups-container');

            if (!colorContainer) {

                return;
            }



            const colorActionsContainer = document.querySelector('.product-color-actions');
            if (!colorActionsContainer) {

                return;
            }

            let colorButton;

            if (colorOption === 'Dark Colors') {
                colorButton = document.querySelector('#product-dark-colors-btn');

            } else if (colorOption === 'Light Colors') {
                colorButton = document.querySelector('#product-light-colors-btn');

            } else if (colorOption === 'All Colors') {
                colorButton = document.querySelector('#product-all-colors-btn');

            }

            if (!colorButton) {


                if (colorOption === 'Dark Colors') {
                    colorButton = document.querySelector('button[id="product-dark-colors-btn"]');

                } else if (colorOption === 'Light Colors') {
                    colorButton = document.querySelector('button[id="product-light-colors-btn"]');

                } else if (colorOption === 'All Colors') {
                    colorButton = document.querySelector('button[id="product-all-colors-btn"]');

                }

                if (!colorButton) {

                    return;
                }
            }


            colorButton.click();


            await this.sleep(300);



        } catch (error) {


        }
    }

    async processTumblerPrices(priceOption) {
        try {


            this.currentOperation = 'processTumblerPrices';

            const isEditorOpen = document.querySelector('.product-editor') !== null;


            if (!isEditorOpen) {


                const editButton = document.querySelector('.TUMBLER-edit-btn');

                if (!editButton) {

                    return;
                }

                const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                const productCard = editButton.closest('.product-card');
                const isCardDisabled = productCard && productCard.classList.contains('disabled');

                if (isButtonDisabled || isCardDisabled) {

                    return;
                }


                editButton.click();


                await this.sleep(750);
            }


            const priceContainer = await this.waitForElement('.product-price-actions');

            if (!priceContainer) {

                return;
            }



            let priceButton;

            if (priceOption === 'Default Prices') {
                priceButton = document.querySelector('#product-default-prices-btn');

            } else if (priceOption === 'Suggested Prices') {
                priceButton = document.querySelector('#product-suggested-prices-btn');

            } else if (priceOption === 'Market Average') {
                priceButton = document.querySelector('#product-market-average-btn');

            } else if (priceOption === 'Max Prices') {
                priceButton = document.querySelector('#product-max-prices-btn');

            }

            if (!priceButton) {


                if (priceOption === 'Default Prices') {
                    priceButton = document.querySelector('button[id="product-default-prices-btn"]');

                } else if (priceOption === 'Suggested Prices') {
                    priceButton = document.querySelector('button[id="product-suggested-prices-btn"]');

                } else if (priceOption === 'Market Average') {
                    priceButton = document.querySelector('button[id="product-market-average-btn"]');

                } else if (priceOption === 'Max Prices') {
                    priceButton = document.querySelector('button[id="product-max-prices-btn"]');

                }

                if (!priceButton) {

                    return;
                }
            }

            const priceButtonId = priceButton.id;



            priceButton.click();


            let buttonDisabled = false;
            let checkCount = 0;
            const maxChecks = 10;

            while (!buttonDisabled && checkCount < maxChecks) {
                await this.sleep(200);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonDisabled = currentButton.disabled ||
                                    currentButton.classList.contains('disabled') ||
                                    currentButton.getAttribute('disabled') !== null;

                    if (buttonDisabled) {

                    }
                }
                checkCount++;
            }


            let buttonEnabled = false;
            checkCount = 0;
            const maxWaitChecks = 30;

            while (!buttonEnabled && checkCount < maxWaitChecks) {
                await this.sleep(300);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonEnabled = !currentButton.disabled &&
                                   !currentButton.classList.contains('disabled') &&
                                   currentButton.getAttribute('disabled') === null;

                    if (buttonEnabled) {

                    }
                }
                checkCount++;
            }

            if (!buttonEnabled) {

            }


            await this.sleep(250);



        } catch (error) {


        }
    }

    rgbToHex(rgb) {
        const match = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
        if (match) {
            const r = parseInt(match[1], 10).toString(16).padStart(2, '0');
            const g = parseInt(match[2], 10).toString(16).padStart(2, '0');
            const b = parseInt(match[3], 10).toString(16).padStart(2, '0');
            return `#${r}${g}${b}`;
        }
        return rgb;
    }

    calculateColorDistance(color1, color2) {
        const r1 = parseInt(color1.slice(1, 3), 16);
        const g1 = parseInt(color1.slice(3, 5), 16);
        const b1 = parseInt(color1.slice(5, 7), 16);
        const r2 = parseInt(color2.slice(1, 3), 16);
        const g2 = parseInt(color2.slice(3, 5), 16);
        const b2 = parseInt(color2.slice(5, 7), 16);
        return Math.sqrt((r2 - r1) ** 2 + (g2 - g1) ** 2 + (b2 - b1) ** 2);
    }

    async waitForBackAndPocketMessages() {
        try {



            await this.waitForAnyGeneratingMessage();



            await this.sleep(3000);


            await this.waitForAnyGeneratingMessage();



            await this.sleep(300);


        } catch (error) {

            throw error;
        }
    }

    async waitForAnyGeneratingMessage(timeout = 300000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            let lastStatusUpdate = 0;

            const checkForMessage = () => {

                let messageVisible = false;

                for (const message of this.productSelectors.generatingMessages) {
                    if (document.body.textContent.includes(message)) {
                        messageVisible = true;
                        break;
                    }
                }

                if (messageVisible) {
                    resolve();
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`Timeout waiting for generating image message after ${Math.floor(timeout/1000)} seconds`));
                } else {
                    if (Date.now() - lastStatusUpdate > 15000) {
                        lastStatusUpdate = Date.now();
                        try { this.updateStatusOnly('Waiting..'); } catch (e) {}
                    }
                    setTimeout(checkForMessage, 500);
                }
            };

            checkForMessage();
        });
    }

    async processTumblerSides(sidesOption) {
        try {

            this.currentOperation = 'processTumblerSides';

            let tabButton;

            if (sidesOption === 'Both Sides' || sidesOption === 'Two Sides') {
                tabButton = Array.from(document.querySelectorAll('.tab-button')).find(
                    btn => btn.textContent.trim().includes('Both Sides') || btn.textContent.trim().includes('Two Sides')
                );

            } else if (sidesOption === 'Default (One Side)') {
                tabButton = Array.from(document.querySelectorAll('.tab-button')).find(
                    btn => btn.textContent.trim().includes('One Side') || btn.textContent.trim() === 'Front'
                );

            }

            if (!tabButton) {

                return;
            }

            if (tabButton.classList.contains('active')) {

            } else {


                await retryWithBackoff(() => { tabButton.click(); return Promise.resolve(); });


                await this.sleep(500);
            }



            await this.sleep(150);

        } catch (error) {

            throw error;
        }
    }

    async processTumblerScale(scaleOption, customScale) {
        try {


            this.currentOperation = 'processTumblerScale';

            const isEditorOpen = document.querySelector('.product-editor') !== null;


            if (!isEditorOpen) {


                const editButton = document.querySelector('.TUMBLER-edit-btn');

                if (!editButton) {

                    return;
                }

                const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                const productCard = editButton.closest('.product-card');
                const isCardDisabled = productCard && productCard.classList.contains('disabled');

                if (isButtonDisabled || isCardDisabled) {

                    return;
                }


                editButton.click();


                await this.sleep(1200);
            }



            const tumblerSettings = this.settings.tumblerProduct || {};
            const desiredSide = tumblerSettings.sides || 'Default (One Side)';


            const sideTabButtons = Array.from(document.querySelectorAll('.tab-button'));


            if (sideTabButtons.length > 0) {

                sideTabButtons.forEach(btn => {

                });
            }

            const activeSideTab = sideTabButtons.find(btn => btn.classList.contains('active'));

            let exactMatchTab = null;

            if (desiredSide === 'Two Sides') {

                exactMatchTab = sideTabButtons.find(btn => btn.textContent.trim() === 'Two Sides');

            } else if (desiredSide === 'Both Sides') {

                exactMatchTab = sideTabButtons.find(btn => btn.textContent.trim() === 'Both Sides');

            } else if (desiredSide === 'Default (One Side)') {

                exactMatchTab = sideTabButtons.find(btn =>
                    btn.textContent.trim() === 'One Side' ||
                    btn.textContent.trim() === 'Default (One Side)' ||
                    btn.textContent.trim() === 'Front'
                );

            }

            if (exactMatchTab && (!activeSideTab || activeSideTab.textContent.trim() !== exactMatchTab.textContent.trim())) {




                await this.sleep(1200);
            }

            else if (!exactMatchTab && !activeSideTab && sideTabButtons.length > 0) {


                const firstTab = sideTabButtons[0];




                await this.sleep(1200);
            }

            else if (activeSideTab) {
                if (exactMatchTab && activeSideTab.textContent.trim() === exactMatchTab.textContent.trim()) {

                } else {

                }
            } else {

            }


            let scaleInput;

            if (scaleOption === 'Keep current') {


            } else if (scaleOption === 'Custom scale') {


                const customRadio = document.querySelector('input#custom[type="radio"]');
                if (!customRadio) {

                    scaleInput = document.querySelector('input[type="radio"][id*="custom"], input[type="radio"][value*="custom"]');
                } else {
                    scaleInput = customRadio;
                }

                if (scaleInput) {

                    scaleInput.click();


                    await this.sleep(500);

                    if (customScale) {


                        let customScaleInputField = document.querySelector('input[type="number"]:not([disabled])');

                        if (!customScaleInputField) {

                            customScaleInputField = document.querySelector('input[type="number"]');

                            if (customScaleInputField && customScaleInputField.disabled) {


                                customScaleInputField.removeAttribute('disabled');

                                await this.sleep(500);
                            }
                        }

                        if (!customScaleInputField) {
    

                            const alternativeInputs = [
                                document.querySelector('input.custom-scale-input'),
                                document.querySelector('input.scale-input'),
                                document.querySelector('input.scale-value'),
                                document.querySelector('input[placeholder*="scale"], input[placeholder*="Scale"]'),
                                document.querySelector('input:not([type="radio"])[id*="scale"], input:not([type="radio"])[id*="Scale"]'),
                                document.querySelector('input:not([type="radio"]):not([type="checkbox"]).ng-untouched'),
                                document.querySelector('input[min="50"][max="100"]'),
                                document.querySelector('input[style*="border-bottom: 2px solid"]')
                            ];

                            customScaleInputField = alternativeInputs.find(input => input !== null);
                        }

                        if (customScaleInputField) {


                            if (customScaleInputField.disabled) {

                                customScaleInputField.disabled = false;
                                customScaleInputField.removeAttribute('disabled');
                            }


                            customScaleInputField.focus();
                            await this.sleep(300);


                            customScaleInputField.value = '';
                            customScaleInputField.dispatchEvent(new Event('input', { bubbles: true }));
                            await this.sleep(300);


                            customScaleInputField.value = customScale;
                            customScaleInputField.dispatchEvent(new Event('input', { bubbles: true }));
                            customScaleInputField.dispatchEvent(new Event('change', { bubbles: true }));


                            customScaleInputField.blur();
                            await this.sleep(300);


                        } else {

                        }
                    }
                }
            } else if (scaleOption === '100%') {

                scaleInput = document.querySelector('input#percent100');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === '85%') {

                scaleInput = document.querySelector('input#percent85');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === '75%') {

                scaleInput = document.querySelector('input#percent75');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === 'Pattern') {

                scaleInput = document.querySelector('input#pattern');
                if (scaleInput) scaleInput.click();
            }

            if (!scaleInput && scaleOption !== 'Keep current') {

                return;
            }

            if (scaleOption !== 'Keep current') {

                await this.sleep(1000);
            }


            const resizeBtn = await this.waitForElement('.resize-btn:not([disabled])');

            if (!resizeBtn) {

                const alternativeResizeButtons = [
                    document.querySelector('button.resize-btn'),
                    document.querySelector('button[class*="resize"]'),
                    document.querySelector('button:contains("Resize")'),
                    document.querySelector('button:contains("Apply")')
                ];

                const resizeButton = alternativeResizeButtons.find(btn => btn !== null && !btn.disabled);

                if (resizeButton) {

                await retryWithBackoff(() => { resizeButton.click(); return Promise.resolve(); });
                } else {

                    return;
                }
            } else {


                await retryWithBackoff(() => { resizeBtn.click(); return Promise.resolve(); });
            }


            try {

                await this.waitForAnyGeneratingMessage(60000);


                await this.waitForGeneratingImageMessages();

            } catch (waitError) {


            }



        } catch (error) {


        }
    }

    hideSnapBulkUploadButton() {
        try {


            const style = document.createElement('style');
            style.id = 'snap-automation-hide-button-style';
            style.textContent = `
                #snap-bulk-upload-btn {
                    display: none !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                    pointer-events: none !important;
                }
            `;
            document.head.appendChild(style);

            const existingButton = document.querySelector('#snap-bulk-upload-btn');
            if (existingButton) {
                existingButton.style.display = 'none';
                existingButton.style.visibility = 'hidden';
                existingButton.style.opacity = '0';
                existingButton.style.pointerEvents = 'none';

            }

            const observer = new MutationObserver((mutations) => {
                for (const mutation of mutations) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        for (const node of mutation.addedNodes) {
                            if (node.nodeType === Node.ELEMENT_NODE) {

                                if (node.id === 'snap-bulk-upload-btn') {
                                    node.style.display = 'none';
                                    node.style.visibility = 'hidden';
                                    node.style.opacity = '0';
                                    node.style.pointerEvents = 'none';

                                }

                                const button = node.querySelector('#snap-bulk-upload-btn');
                                if (button) {
                                    button.style.display = 'none';
                                    button.style.visibility = 'hidden';
                                    button.style.opacity = '0';
                                    button.style.pointerEvents = 'none';

                                }
                            }
                        }
                    }
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });


        } catch (error) {


        }
    }
}

window.snapAutomation = new SnapAutomation();